#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KlingCreator Modern GUI
Modern PyQt6 interface with Material Design
"""

import sys
import os
from typing import Dict, List, Optional

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QLineEdit, QTextEdit,
                            QComboBox, QCheckBox, QTabWidget, QSplitter, QFrame,
                            QGridLayout, QScrollArea, QFileDialog, QMessageBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QSettings
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor

# Import our custom components
from modern_themes import ModernThemes
from modern_components import (ModernCard, ModernButton, StatusIndicator, 
                              ModernProgressBar, Session<PERSON>ard, ModernScrollArea)

# Import kling wrapper
from kling_wrapper import <PERSON>Gen, ImageGen


class ModernKlingCreator(QMainWindow):
    """Modern KlingCreator main window"""
    
    def __init__(self):
        super().__init__()
        self.sessions = {}
        self.current_theme = 'dark'
        self.settings = QSettings('KlingCreator', 'Modern')
        
        self.setup_ui()
        self.apply_theme()
        self.load_settings()
        
    def setup_ui(self):
        """Setup main UI"""
        self.setWindowTitle("KlingCreator Modern")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Sessions and Controls
        self.setup_left_panel(splitter)
        
        # Right panel - Generation and Results
        self.setup_right_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        # Setup menu bar
        self.setup_menu_bar()
        
        # Setup status bar
        self.setup_status_bar()
        
    def setup_left_panel(self, parent):
        """Setup left control panel"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(16)
        
        # Header
        header_card = ModernCard("KlingCreator", "AI Video & Image Generation")
        left_layout.addWidget(header_card)
        
        # Theme switcher
        theme_card = ModernCard("Appearance")
        theme_layout = QHBoxLayout()
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Dark Theme", "Light Theme"])
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        theme_layout.addWidget(QLabel("Theme:"))
        theme_layout.addWidget(self.theme_combo)
        theme_layout.addStretch()
        
        theme_card.add_layout(theme_layout)
        left_layout.addWidget(theme_card)
        
        # Sessions section
        sessions_card = ModernCard("Sessions")
        
        # Add session button
        add_session_btn = ModernButton("+ Add Session", "primary")
        add_session_btn.clicked.connect(self.add_session)
        sessions_card.add_widget(add_session_btn)
        
        # Sessions scroll area
        self.sessions_scroll = ModernScrollArea()
        self.sessions_widget = QWidget()
        self.sessions_layout = QVBoxLayout(self.sessions_widget)
        self.sessions_layout.setContentsMargins(0, 0, 0, 0)
        self.sessions_layout.setSpacing(12)
        self.sessions_layout.addStretch()
        
        self.sessions_scroll.setWidget(self.sessions_widget)
        sessions_card.add_widget(self.sessions_scroll)
        
        left_layout.addWidget(sessions_card)
        
        # Settings card
        settings_card = ModernCard("Settings")
        
        # Download settings
        self.auto_download_cb = QCheckBox("Auto Download")
        self.no_watermark_cb = QCheckBox("No Watermark")
        
        settings_card.add_widget(self.auto_download_cb)
        settings_card.add_widget(self.no_watermark_cb)
        
        # Download path
        download_layout = QHBoxLayout()
        self.download_path_edit = QLineEdit()
        self.download_path_edit.setPlaceholderText("Download folder...")
        browse_btn = ModernButton("Browse", "secondary")
        browse_btn.clicked.connect(self.browse_download_folder)
        
        download_layout.addWidget(self.download_path_edit)
        download_layout.addWidget(browse_btn)
        settings_card.add_layout(download_layout)
        
        left_layout.addWidget(settings_card)
        
        parent.addWidget(left_widget)
        
    def setup_right_panel(self, parent):
        """Setup right generation panel"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(16)
        
        # Generation controls
        gen_card = ModernCard("Generation")
        
        # Tabs for video/image
        self.gen_tabs = QTabWidget()
        
        # Video tab
        video_tab = QWidget()
        video_layout = QVBoxLayout(video_tab)
        
        # Prompt
        video_layout.addWidget(QLabel("Prompt:"))
        self.video_prompt = QTextEdit()
        self.video_prompt.setPlaceholderText("Describe your video...")
        self.video_prompt.setMaximumHeight(100)
        video_layout.addWidget(self.video_prompt)
        
        # Model selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Model:"))
        self.video_model = QComboBox()
        self.video_model.addItems(["1.0", "1.5", "1.6"])
        self.video_model.setCurrentText("1.6")
        model_layout.addWidget(self.video_model)
        model_layout.addStretch()
        video_layout.addLayout(model_layout)
        
        # Image input
        image_layout = QHBoxLayout()
        self.video_image_path = QLineEdit()
        self.video_image_path.setPlaceholderText("Optional: Select image...")
        image_browse_btn = ModernButton("Browse", "secondary")
        image_browse_btn.clicked.connect(self.browse_video_image)
        image_layout.addWidget(self.video_image_path)
        image_layout.addWidget(image_browse_btn)
        video_layout.addLayout(image_layout)
        
        # Generate button
        self.generate_video_btn = ModernButton("Generate Video", "primary")
        self.generate_video_btn.clicked.connect(self.generate_video)
        self.generate_video_btn.setEnabled(False)
        video_layout.addWidget(self.generate_video_btn)
        
        self.gen_tabs.addTab(video_tab, "Video")
        
        # Image tab
        image_tab = QWidget()
        image_layout = QVBoxLayout(image_tab)
        
        # Prompt
        image_layout.addWidget(QLabel("Prompt:"))
        self.image_prompt = QTextEdit()
        self.image_prompt.setPlaceholderText("Describe your image...")
        self.image_prompt.setMaximumHeight(100)
        image_layout.addWidget(self.image_prompt)
        
        # Generate button
        self.generate_image_btn = ModernButton("Generate Image", "primary")
        self.generate_image_btn.clicked.connect(self.generate_image)
        self.generate_image_btn.setEnabled(False)
        image_layout.addWidget(self.generate_image_btn)
        
        self.gen_tabs.addTab(image_tab, "Image")
        
        gen_card.add_widget(self.gen_tabs)
        right_layout.addWidget(gen_card)
        
        # Results area
        results_card = ModernCard("Results")
        
        # Results will be added dynamically
        self.results_layout = QVBoxLayout()
        results_card.add_layout(self.results_layout)
        
        right_layout.addWidget(results_card)
        
        parent.addWidget(right_widget)
        
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('File')
        file_menu.addAction('New Session', self.add_session)
        file_menu.addSeparator()
        file_menu.addAction('Settings', self.show_settings)
        file_menu.addSeparator()
        file_menu.addAction('Exit', self.close)
        
        # View menu
        view_menu = menubar.addMenu('View')
        view_menu.addAction('Dark Theme', lambda: self.change_theme("Dark Theme"))
        view_menu.addAction('Light Theme', lambda: self.change_theme("Light Theme"))
        
        # Help menu
        help_menu = menubar.addMenu('Help')
        help_menu.addAction('About', self.show_about)
        
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready")
        
        # Add permanent widgets
        self.status_indicator = StatusIndicator("idle", "Ready")
        self.status_bar.addPermanentWidget(self.status_indicator)
        
    def apply_theme(self):
        """Apply current theme"""
        stylesheet = ModernThemes.get_stylesheet(self.current_theme)
        self.setStyleSheet(stylesheet)
        
    def change_theme(self, theme_name):
        """Change application theme"""
        self.current_theme = 'dark' if 'Dark' in theme_name else 'light'
        self.apply_theme()
        self.save_settings()
        
    def add_session(self):
        """Add new session"""
        session_id = f"session_{len(self.sessions) + 1}"
        
        # Create session card
        session_card = SessionCard(session_id, f"Session {len(self.sessions) + 1}")
        session_card.connect_clicked.connect(self.connect_session)
        session_card.disconnect_clicked.connect(self.disconnect_session)
        session_card.generate_clicked.connect(self.quick_generate)
        
        # Insert before stretch
        self.sessions_layout.insertWidget(self.sessions_layout.count() - 1, session_card)
        
        # Store session
        self.sessions[session_id] = {
            'card': session_card,
            'video_gen': None,
            'image_gen': None,
            'connected': False
        }
        
    def connect_session(self, session_id):
        """Connect session with cookie"""
        cookie, ok = self.get_cookie_input()
        if not ok or not cookie:
            return
            
        session = self.sessions[session_id]
        session_card = session['card']
        
        try:
            session_card.update_status("connecting", "Connecting...")
            
            # Create generators
            session['video_gen'] = VideoGen(cookie)
            session['image_gen'] = ImageGen(cookie)
            
            # Test connection
            points = session['video_gen'].get_account_point()
            if points is None:
                raise Exception("Invalid cookie or connection failed")
                
            session['connected'] = True
            session_card.update_status("connected", f"Connected - {points} points")
            
            # Enable generation buttons
            self.update_generation_buttons()
            
        except Exception as e:
            session_card.update_status("error", f"Connection failed: {str(e)}")
            
    def disconnect_session(self, session_id):
        """Disconnect session"""
        session = self.sessions[session_id]
        session_card = session['card']
        
        session['video_gen'] = None
        session['image_gen'] = None
        session['connected'] = False
        
        session_card.update_status("idle", "Disconnected")
        self.update_generation_buttons()
        
    def update_generation_buttons(self):
        """Update generation button states"""
        has_connected = any(s['connected'] for s in self.sessions.values())
        self.generate_video_btn.setEnabled(has_connected)
        self.generate_image_btn.setEnabled(has_connected)
        
    def get_cookie_input(self):
        """Get cookie from user input"""
        from PyQt6.QtWidgets import QInputDialog
        
        cookie, ok = QInputDialog.getMultiLineText(
            self, 
            "Session Cookie", 
            "Enter your session cookie:",
            ""
        )
        return cookie.strip(), ok
        
    def browse_download_folder(self):
        """Browse for download folder"""
        folder = QFileDialog.getExistingDirectory(self, "Select Download Folder")
        if folder:
            self.download_path_edit.setText(folder)
            
    def browse_video_image(self):
        """Browse for video input image"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Select Image", 
            "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            self.video_image_path.setText(file_path)
            
    def generate_video(self):
        """Generate video"""
        prompt = self.video_prompt.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "Warning", "Please enter a prompt")
            return
            
        # Find connected session
        connected_session = None
        for session in self.sessions.values():
            if session['connected']:
                connected_session = session
                break
                
        if not connected_session:
            QMessageBox.warning(self, "Warning", "No connected sessions")
            return
            
        # TODO: Implement video generation with threading
        QMessageBox.information(self, "Info", "Video generation will be implemented")
        
    def generate_image(self):
        """Generate image"""
        prompt = self.image_prompt.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "Warning", "Please enter a prompt")
            return
            
        # Find connected session
        connected_session = None
        for session in self.sessions.values():
            if session['connected']:
                connected_session = session
                break
                
        if not connected_session:
            QMessageBox.warning(self, "Warning", "No connected sessions")
            return
            
        # TODO: Implement image generation with threading
        QMessageBox.information(self, "Info", "Image generation will be implemented")
        
    def quick_generate(self, session_id):
        """Quick generate from session card"""
        QMessageBox.information(self, "Info", f"Quick generate for {session_id}")
        
    def show_settings(self):
        """Show settings dialog"""
        QMessageBox.information(self, "Settings", "Settings dialog will be implemented")
        
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About KlingCreator Modern", 
                         "KlingCreator Modern\n\nModern PyQt6 interface for AI video and image generation")
        
    def save_settings(self):
        """Save application settings"""
        self.settings.setValue('theme', self.current_theme)
        self.settings.setValue('download_path', self.download_path_edit.text())
        self.settings.setValue('auto_download', self.auto_download_cb.isChecked())
        self.settings.setValue('no_watermark', self.no_watermark_cb.isChecked())
        
    def load_settings(self):
        """Load application settings"""
        theme = self.settings.value('theme', 'dark')
        self.current_theme = theme
        self.theme_combo.setCurrentText("Dark Theme" if theme == 'dark' else "Light Theme")
        
        download_path = self.settings.value('download_path', '')
        self.download_path_edit.setText(download_path)
        
        auto_download = self.settings.value('auto_download', False, type=bool)
        self.auto_download_cb.setChecked(auto_download)
        
        no_watermark = self.settings.value('no_watermark', False, type=bool)
        self.no_watermark_cb.setChecked(no_watermark)
        
    def closeEvent(self, event):
        """Handle close event"""
        self.save_settings()
        event.accept()


def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("KlingCreator Modern")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("KlingCreator")
    
    # Create and show main window
    window = ModernKlingCreator()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
