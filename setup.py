from setuptools import find_packages, setup

setup(
    name="kling-creator",
    version="0.3.3",
    author="yihong0618",
    author_email="<EMAIL>",
    description="High quality video generation by https://klingai.kuaishou.com/. Reverse engineered API.",
    url="https://github.com/yihong0618/klingCreator",
    install_requires=[
        "requests",
        "fake-useragent",
        "rich",
    ],
    packages=find_packages(exclude=["tests", "tests.*"]),
    entry_points={
        "console_scripts": ["kling = kling.kling:main"],
    },
    classifiers=[
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
