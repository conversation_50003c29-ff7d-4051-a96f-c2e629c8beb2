#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
KlingCreator Modern Launcher
Launch the modern PyQt6 interface
"""

import sys
import os

def check_requirements():
    """Check if PyQt6 is installed"""
    try:
        import PyQt6
        print("✅ PyQt6 is installed")
        return True
    except ImportError:
        print("❌ PyQt6 is not installed")
        print("Please install PyQt6:")
        print("pip install -r requirements_modern.txt")
        return False

def main():
    """Main launcher function"""
    print("🚀 KlingCreator Modern Launcher")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        input("Press Enter to exit...")
        return
    
    # Import and run the modern app
    try:
        from kling_creator_modern import main as run_modern_app
        print("🎨 Starting KlingCreator Modern...")
        run_modern_app()
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
