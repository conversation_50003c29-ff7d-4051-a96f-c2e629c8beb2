#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Modern Themes for KlingCreator
Material Design inspired themes
"""

class ModernThemes:
    """Modern theme definitions"""
    
    DARK_THEME = {
        'name': 'Dark',
        'colors': {
            'background': '#121212',
            'surface': '#1e1e1e',
            'surface_variant': '#2d2d2d',
            'primary': '#bb86fc',
            'primary_variant': '#985eff',
            'secondary': '#03dac6',
            'secondary_variant': '#018786',
            'error': '#cf6679',
            'warning': '#ffb74d',
            'success': '#4caf50',
            'text_primary': '#ffffff',
            'text_secondary': '#b3b3b3',
            'text_disabled': '#666666',
            'border': '#333333',
            'hover': '#2d2d2d',
            'pressed': '#404040',
            'selected': '#bb86fc20'
        }
    }
    
    LIGHT_THEME = {
        'name': 'Light',
        'colors': {
            'background': '#fafafa',
            'surface': '#ffffff',
            'surface_variant': '#f5f5f5',
            'primary': '#6200ea',
            'primary_variant': '#3700b3',
            'secondary': '#018786',
            'secondary_variant': '#00695c',
            'error': '#b00020',
            'warning': '#ff8f00',
            'success': '#2e7d32',
            'text_primary': '#000000',
            'text_secondary': '#666666',
            'text_disabled': '#999999',
            'border': '#e0e0e0',
            'hover': '#f5f5f5',
            'pressed': '#eeeeee',
            'selected': '#6200ea20'
        }
    }
    
    @staticmethod
    def get_stylesheet(theme_name='dark'):
        """Get complete stylesheet for theme"""
        theme = ModernThemes.DARK_THEME if theme_name == 'dark' else ModernThemes.LIGHT_THEME
        colors = theme['colors']
        
        return f"""
        /* Main Application */
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
            font-family: 'Segoe UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
        }}
        
        /* Cards and Panels */
        .card {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 12px;
            padding: 16px;
        }}
        
        .card:hover {{
            border-color: {colors['primary']};
            box-shadow: 0 4px 12px rgba(187, 134, 252, 0.15);
        }}
        
        /* Buttons */
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 14px;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: {colors['primary_variant']};
            transform: translateY(-1px);
        }}
        
        QPushButton:pressed {{
            background-color: {colors['primary_variant']};
            transform: translateY(0px);
        }}
        
        QPushButton:disabled {{
            background-color: {colors['text_disabled']};
            color: {colors['text_secondary']};
        }}
        
        /* Secondary Button */
        QPushButton.secondary {{
            background-color: {colors['surface_variant']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
        }}
        
        QPushButton.secondary:hover {{
            background-color: {colors['hover']};
            border-color: {colors['primary']};
        }}
        
        /* Danger Button */
        QPushButton.danger {{
            background-color: {colors['error']};
        }}
        
        QPushButton.danger:hover {{
            background-color: #d32f2f;
        }}
        
        /* Input Fields */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 2px solid {colors['border']};
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {colors['primary']};
            outline: none;
        }}
        
        /* ComboBox */
        QComboBox {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 2px solid {colors['border']};
            border-radius: 8px;
            padding: 8px 12px;
            min-height: 20px;
        }}
        
        QComboBox:focus {{
            border-color: {colors['primary']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {colors['text_secondary']};
            margin-right: 10px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            border-radius: 8px;
            selection-background-color: {colors['selected']};
        }}
        
        /* CheckBox and RadioButton */
        QCheckBox, QRadioButton {{
            color: {colors['text_primary']};
            font-size: 14px;
            spacing: 8px;
        }}
        
        QCheckBox::indicator, QRadioButton::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {colors['border']};
            border-radius: 4px;
            background-color: {colors['surface']};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {colors['primary']};
            border-color: {colors['primary']};
        }}
        
        QRadioButton::indicator {{
            border-radius: 9px;
        }}
        
        QRadioButton::indicator:checked {{
            background-color: {colors['primary']};
            border-color: {colors['primary']};
        }}
        
        /* Progress Bar */
        QProgressBar {{
            background-color: {colors['surface_variant']};
            border: none;
            border-radius: 8px;
            height: 8px;
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {colors['primary']};
            border-radius: 8px;
        }}
        
        /* Tabs */
        QTabWidget::pane {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 8px;
            margin-top: 4px;
        }}
        
        QTabBar::tab {{
            background-color: {colors['surface_variant']};
            color: {colors['text_secondary']};
            padding: 12px 24px;
            margin-right: 4px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {colors['hover']};
            color: {colors['text_primary']};
        }}
        
        /* ScrollBar */
        QScrollBar:vertical {{
            background-color: {colors['surface_variant']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['text_disabled']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['text_secondary']};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        /* Labels */
        QLabel {{
            color: {colors['text_primary']};
            font-size: 14px;
        }}
        
        .title {{
            font-size: 24px;
            font-weight: 700;
            color: {colors['text_primary']};
            margin-bottom: 8px;
        }}
        
        .subtitle {{
            font-size: 18px;
            font-weight: 600;
            color: {colors['text_primary']};
            margin-bottom: 4px;
        }}
        
        .caption {{
            font-size: 12px;
            color: {colors['text_secondary']};
        }}
        
        /* Status indicators */
        .status-success {{
            color: {colors['success']};
            font-weight: 600;
        }}
        
        .status-error {{
            color: {colors['error']};
            font-weight: 600;
        }}
        
        .status-warning {{
            color: {colors['warning']};
            font-weight: 600;
        }}
        
        /* Animations */
        * {{
            transition: all 0.2s ease-in-out;
        }}
        """
    
    @staticmethod
    def get_card_style():
        """Get card widget style"""
        return "card"
    
    @staticmethod
    def get_button_styles():
        """Get button style classes"""
        return {
            'primary': '',
            'secondary': 'secondary',
            'danger': 'danger'
        }
