#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Modern UI Components for KlingCreator
Custom widgets with modern design
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QProgressBar, QScrollArea,
                            QGraphicsDropShadowEffect, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QPixmap, QPainter, QPainterPath, QColor, QFont


class ModernCard(QFrame):
    """Modern card widget with shadow and hover effects"""
    
    def __init__(self, title="", subtitle="", parent=None):
        super().__init__(parent)
        self.setObjectName("card")
        self.setup_ui(title, subtitle)
        self.setup_shadow()
        
    def setup_ui(self, title, subtitle):
        """Setup card UI"""
        self.setFrameStyle(QFrame.Shape.NoFrame)
        self.setStyleSheet("QFrame#card { background-color: #1e1e1e; border-radius: 12px; }")
        
        # Main layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 16, 20, 16)
        self.layout.setSpacing(12)
        
        # Header
        if title or subtitle:
            self.header_layout = QVBoxLayout()
            self.header_layout.setSpacing(4)
            
            if title:
                self.title_label = QLabel(title)
                self.title_label.setObjectName("title")
                self.title_label.setStyleSheet("font-size: 18px; font-weight: 600; color: #ffffff;")
                self.header_layout.addWidget(self.title_label)
                
            if subtitle:
                self.subtitle_label = QLabel(subtitle)
                self.subtitle_label.setObjectName("subtitle")
                self.subtitle_label.setStyleSheet("font-size: 14px; color: #b3b3b3;")
                self.header_layout.addWidget(self.subtitle_label)
                
            self.layout.addLayout(self.header_layout)
        
        # Content area
        self.content_layout = QVBoxLayout()
        self.layout.addLayout(self.content_layout)
        
    def setup_shadow(self):
        """Add drop shadow effect"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(4)
        shadow.setColor(QColor(0, 0, 0, 60))
        self.setGraphicsEffect(shadow)
        
    def add_widget(self, widget):
        """Add widget to content area"""
        self.content_layout.addWidget(widget)
        
    def add_layout(self, layout):
        """Add layout to content area"""
        self.content_layout.addLayout(layout)


class ModernButton(QPushButton):
    """Modern button with animations"""
    
    def __init__(self, text="", button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_style()
        
    def setup_style(self):
        """Setup button style based on type"""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 600;
                font-size: 14px;
                min-height: 20px;
            }
        """
        
        if self.button_type == "primary":
            style = base_style + """
                QPushButton {
                    background-color: #bb86fc;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #d7b3ff;
                }
                QPushButton:pressed {
                    background-color: #985eff;
                }
            """
        elif self.button_type == "secondary":
            style = base_style + """
                QPushButton {
                    background-color: #2d2d2d;
                    color: #ffffff;
                    border: 1px solid #333333;
                }
                QPushButton:hover {
                    background-color: #404040;
                    border-color: #bb86fc;
                }
            """
        elif self.button_type == "danger":
            style = base_style + """
                QPushButton {
                    background-color: #cf6679;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """
        else:
            style = base_style
            
        self.setStyleSheet(style)


class StatusIndicator(QWidget):
    """Status indicator with colored dot and text"""
    
    def __init__(self, status="idle", text="", parent=None):
        super().__init__(parent)
        self.status = status
        self.setup_ui(text)
        
    def setup_ui(self, text):
        """Setup status indicator UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Status dot
        self.dot = QLabel()
        self.dot.setFixedSize(12, 12)
        self.update_status(self.status)
        layout.addWidget(self.dot)
        
        # Status text
        self.text_label = QLabel(text)
        self.text_label.setStyleSheet("color: #b3b3b3; font-size: 14px;")
        layout.addWidget(self.text_label)
        
        layout.addStretch()
        
    def update_status(self, status, text=""):
        """Update status and text"""
        self.status = status
        
        colors = {
            'idle': '#666666',
            'connecting': '#ffb74d',
            'connected': '#4caf50',
            'generating': '#bb86fc',
            'completed': '#4caf50',
            'error': '#cf6679'
        }
        
        color = colors.get(status, '#666666')
        self.dot.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: 6px;
            }}
        """)
        
        if text:
            self.text_label.setText(text)


class ModernProgressBar(QWidget):
    """Modern progress bar with label"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup progress bar UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Progress label
        self.label = QLabel("Ready")
        self.label.setStyleSheet("color: #b3b3b3; font-size: 14px;")
        layout.addWidget(self.label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                background-color: #2d2d2d;
                border: none;
                border-radius: 8px;
                height: 8px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #bb86fc;
                border-radius: 8px;
            }
        """)
        self.progress_bar.setTextVisible(False)
        layout.addWidget(self.progress_bar)
        
    def set_progress(self, value, text=""):
        """Set progress value and text"""
        self.progress_bar.setValue(value)
        if text:
            self.label.setText(text)


class SessionCard(ModernCard):
    """Session card with status and controls"""
    
    # Signals
    connect_clicked = pyqtSignal(str)
    disconnect_clicked = pyqtSignal(str)
    generate_clicked = pyqtSignal(str)
    
    def __init__(self, session_id, session_name="", parent=None):
        self.session_id = session_id
        super().__init__(f"Session {session_id}", session_name, parent)
        self.setup_session_ui()
        
    def setup_session_ui(self):
        """Setup session-specific UI"""
        # Status indicator
        self.status_indicator = StatusIndicator("idle", "Disconnected")
        self.add_widget(self.status_indicator)
        
        # Progress bar
        self.progress_bar = ModernProgressBar()
        self.add_widget(self.progress_bar)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        self.connect_btn = ModernButton("Connect", "primary")
        self.connect_btn.clicked.connect(lambda: self.connect_clicked.emit(self.session_id))
        controls_layout.addWidget(self.connect_btn)
        
        self.disconnect_btn = ModernButton("Disconnect", "secondary")
        self.disconnect_btn.clicked.connect(lambda: self.disconnect_clicked.emit(self.session_id))
        self.disconnect_btn.setEnabled(False)
        controls_layout.addWidget(self.disconnect_btn)
        
        self.generate_btn = ModernButton("Generate", "primary")
        self.generate_btn.clicked.connect(lambda: self.generate_clicked.emit(self.session_id))
        self.generate_btn.setEnabled(False)
        controls_layout.addWidget(self.generate_btn)
        
        controls_layout.addStretch()
        self.add_layout(controls_layout)
        
    def update_status(self, status, message=""):
        """Update session status"""
        self.status_indicator.update_status(status, message)
        
        # Update button states
        if status == "connected":
            self.connect_btn.setEnabled(False)
            self.disconnect_btn.setEnabled(True)
            self.generate_btn.setEnabled(True)
        elif status == "idle":
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
            self.generate_btn.setEnabled(False)
        elif status == "generating":
            self.connect_btn.setEnabled(False)
            self.disconnect_btn.setEnabled(False)
            self.generate_btn.setEnabled(False)
            
    def update_progress(self, value, text=""):
        """Update progress"""
        self.progress_bar.set_progress(value, text)


class ModernScrollArea(QScrollArea):
    """Modern scroll area with custom styling"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
        
    def setup_style(self):
        """Setup scroll area style"""
        self.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #2d2d2d;
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }
            QScrollBar::handle:vertical {
                background-color: #666666;
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #888888;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
