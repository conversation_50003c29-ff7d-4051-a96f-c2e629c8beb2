#!/usr/bin/env python3
"""
Debug build script for KlingCreator - with console for error debugging
"""

import os
import sys
import subprocess
import shutil

def build_debug_exe():
    """Build .exe with console for debugging"""
    print("🔨 Building DEBUG .exe file (with console)...")
    
    # Clean previous builds
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # PyInstaller command - debug version with console
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single .exe file
        "--console",                    # SHOW console for debugging
        "--name=KlingCreator_Debug",    # Debug name
        "--add-data=kling_wrapper.py;.", # Include wrapper
        "--add-data=kling;kling",       # Include kling folder
        # Core imports
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--collect-all=PyQt6",
        # Standard imports
        "--hidden-import=requests",
        "--hidden-import=PIL",
        "--hidden-import=json",
        "--hidden-import=threading",
        "--hidden-import=uuid",
        "--hidden-import=datetime",
        # Fix fake_useragent
        "--hidden-import=fake_useragent",
        "--hidden-import=fake_useragent.fake",
        "--hidden-import=fake_useragent.utils",
        "--collect-all=fake_useragent",
        "--hidden-import=rich",
        "--collect-all=rich",
        # Verbose output
        "--log-level=INFO",  # Reduce verbosity
        "unified_multi_session.py"
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False, text=True)
        print("✅ Debug build completed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Debug build failed: {e}")
        return False

def main():
    """Main debug build process"""
    print("🐛 KlingCreator DEBUG .exe Builder")
    print("=" * 40)
    print("This version shows console for error debugging")
    print()
    
    # Check if main file exists
    if not os.path.exists("unified_multi_session.py"):
        print("❌ unified_multi_session.py not found!")
        return False
    
    # Build debug .exe
    if not build_debug_exe():
        return False
    
    # Check output
    exe_path = os.path.join("dist", "KlingCreator_Debug.exe")
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"🎉 Debug build success! Created: {exe_path}")
        print(f"📊 File size: {file_size:.1f} MB")
        print()
        print("🐛 This version will show console window with error messages")
        print("💡 Run it to see detailed error information")
        return True
    else:
        print("❌ Debug .exe file not found!")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
