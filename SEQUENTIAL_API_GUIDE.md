# 🔄 Sequential API with Delay Guide

## 📋 Overview
KlingCreator Modern now supports **Sequential API with Delay** mode to prevent API overload by spacing out requests with configurable delays.

## 🎯 What is Sequential with Delay Mode?

### 🔄 Sequential with Delay (NEW)
- **API Requests**: Sent with time delays (Session 1 → Wait 2s → Session 2 → Wait 2s → Session 3...)
- **Downloads**: Independent and can run in parallel
- **Purpose**: Prevent API server overload with controlled timing
- **Timeline**: Request 1 → Delay → Request 2 → Delay → Request 3...

### ⚡ Parallel API Calls (Original)
- **API Requests**: All sent simultaneously
- **Downloads**: Independent and can run in parallel  
- **Purpose**: Faster execution
- **Timeline**: Request 1, 2, 3... all sent at once

## 🚀 How It Works

### Sequential with Delay Flow Example:
```
🔄 Sequential: Starting timer-based execution with 2s delays
🔄 Sequential: Sending request 1/3 (Session: abc123)
📝 [abc123] Starting video generation...
⏰ Waiting 2 seconds...
🔄 Sequential: Sending request 2/3 (Session: def456)
📝 [def456] Starting video generation...
⏰ Waiting 2 seconds...
🔄 Sequential: Sending request 3/3 (Session: ghi789)
📝 [ghi789] Starting video generation...
🎉 Sequential: All 3 requests sent with delays
```

### Key Points:
1. **API calls are spaced with delays** (configurable 1-5 seconds)
2. **Downloads are independent** (can happen in parallel)
3. **Next request sent after delay** (not waiting for response)
4. **Prevents API overload** with controlled timing

## 🎛️ Usage Instructions

### Step 1: Select Execution Mode
1. In **Generation Controls** section
2. Find **"Execution Mode"** dropdown
3. Choose:
   - **🔄 Sequential with Delay** (Recommended for stability)
   - **⚡ Parallel (All at once)** (Faster but may overload server)

### Step 2: Configure Delay (Sequential Mode Only)
1. Find **"Sequential Delay"** dropdown
2. Choose delay time:
   - **1 second** (Fast but less safe)
   - **2 seconds** (Default, balanced)
   - **3 seconds** (Conservative)
   - **5 seconds** (Very safe for heavy loads)

### Step 3: Execute Generation
1. Set up your sessions, prompts, and images
2. Click **"🔄 Execute Sequential API"** or **"⚡ Execute Parallel API"**
3. Watch the logs to see the execution flow

## 📊 Benefits of Sequential Mode

### ✅ Advantages:
- **API Stability**: Reduces server overload
- **Lower Error Rate**: Less likely to get API errors
- **Predictable Flow**: Clear execution order
- **Resource Management**: Better server resource usage

### ⚠️ Considerations:
- **Slightly Slower**: Takes more time than parallel
- **Still Efficient**: Downloads can run in parallel
- **Recommended**: For large batches (5+ sessions)

## 🔍 Log Examples

### Sequential Mode Logs:
```
🚀 Starting batch video generation on 3 sessions with random images from folder (15 images available)
🔄 Sequential API: Sending request 1/3 (Session: abc123)
🎲 [abc123] Selected random image: mountain.jpg
🚀 [abc123] Started video generation
📝 [abc123] Prompt: Beautiful landscape...
🖼️ [abc123] Using image: mountain.jpg
✅ [abc123] Generated 1 video(s)
🔄 Sequential API: Session abc123 completed, starting session 2/3
```

### Error Handling:
```
❌ [def456] Error: API Response Error: Video generation failed
🔄 Sequential API: Session def456 failed, continuing to session 3/3
📝 [ghi789] Starting video generation...
```

## 💡 Best Practices

1. **Use Sequential for Large Batches**: 5+ sessions
2. **Use Parallel for Small Batches**: 1-3 sessions
3. **Monitor Logs**: Check execution flow in Logs tab
4. **API Limits**: Sequential helps stay within rate limits
5. **Downloads**: Auto-download works independently in both modes

## 🔧 Technical Details

- **Queue Management**: Sessions are queued and processed in order
- **Error Recovery**: Failed sessions don't stop the queue
- **State Management**: Current session index is tracked
- **Download Independence**: Downloads start immediately after API response
- **Memory Efficient**: Only one API call active at a time

This feature ensures reliable video generation even with many sessions while maintaining the flexibility of parallel downloads!
