#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Launcher cho Unified Multi-Session KlingCreator
"""

import sys
import os

# Thêm thư mục hiện tại vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from unified_multi_session import main
    
    if __name__ == '__main__':
        print("🚀 Khởi động Unified Multi-Session KlingCreator...")
        print("=" * 60)
        print("✨ NEW FEATURES:")
        print("🍪 Cookie Management:")
        print("   • Manual input hoặc import từ .txt file")
        print("   • Real-time connection status và points tracking")
        print("   • Bulk operations trên tất cả sessions")
        print()
        print("🖼️ Advanced Image Selection:")
        print("   • Single File: Tất cả sessions dùng cùng 1 ảnh")
        print("   • Folder Mode: Mỗi session random pick ảnh khác nhau")
        print()
        print("📝 Smart Prompt System:")
        print("   • Manual: Nhập prompt, tất cả sessions dùng chung")
        print("   • File-based: Import prompts, mỗi session random pick")
        print()
        print("⚡ Unified Interface:")
        print("   • Single window cho tất cả operations")
        print("   • Real-time progress tracking")
        print("   • Integrated results management")
        print("=" * 60)
        
        main()
        
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
    print("Vui lòng đảm bảo đã cài đặt:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ Lỗi khởi động: {e}")
    sys.exit(1)
