#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Unified Multi-Session KlingCreator - Modern GUI
Single window với multi-threading cho nhiều cookies
Rebuilt with PyQt6 and modern design
"""

import sys
import os
import uuid
import threading
import random
import time
import json
import subprocess
import platform
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QPushButton, QListWidget,
                             QTextEdit, QComboBox, QCheckBox, QLineEdit,
                             QListWidgetItem, QGroupBox, QGridLayout, QProgressBar,
                             QFileDialog, QMessageBox, QSplitter, QTabWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QRadioButton, QButtonGroup, QScrollArea, Q<PERSON>ialog,
                             QMenu, <PERSON><PERSON><PERSON><PERSON>, QSpacer<PERSON><PERSON>, QSizePolicy, QSpinBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QTimer, QSettings, QSize
from PyQt6.QtGui import QPixmap, QFont, QIcon, QColor, QAction, QPalette

from kling_wrapper import ImageGen, VideoGen
import requests
from PIL import Image
import io


class DownloadSettings:
    """Quản lý download settings"""
    def __init__(self):
        self.settings = QSettings("KlingCreator", "UnifiedMultiSession")
        self.download_path = self.settings.value("download_path", "./downloads")
        self.auto_download_enabled = self.settings.value("auto_download_enabled", True, type=bool)
        self.no_watermark_mode = self.settings.value("no_watermark_mode", False, type=bool)
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds

    def save_settings(self):
        """Lưu settings"""
        self.settings.setValue("download_path", self.download_path)
        self.settings.setValue("auto_download_enabled", self.auto_download_enabled)
        self.settings.setValue("no_watermark_mode", self.no_watermark_mode)

    def ensure_download_directory(self):
        """Đảm bảo download directory tồn tại"""
        if not os.path.exists(self.download_path):
            os.makedirs(self.download_path, exist_ok=True)
        return os.path.exists(self.download_path) and os.access(self.download_path, os.W_OK)


class DownloadTask:
    """Thông tin về một download task"""
    def __init__(self, session_id: str, url: str, content_type: str, model: str, index: int, no_watermark: bool = False):
        self.session_id = session_id
        self.url = url
        self.content_type = content_type
        self.model = model
        self.index = index
        self.no_watermark = no_watermark
        self.status = "pending"  # pending, downloading, completed, failed
        self.progress = 0
        self.file_path = ""
        self.error_message = ""
        self.retry_count = 0

    def generate_filename(self, download_path: str) -> str:
        """Tạo filename unique"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        extension = ".mp4" if self.content_type == "video" else ".png"
        wm_suffix = "_no_wm" if self.no_watermark else ""
        filename = f"{self.session_id}_{timestamp}_{self.content_type}_{self.model}_{self.index:03d}{wm_suffix}{extension}"
        return os.path.join(download_path, filename)


class DownloadWorkerThread(QThread):
    """Worker thread cho download operations"""
    download_progress = pyqtSignal(str, int)  # task_id, progress
    download_completed = pyqtSignal(str, str)  # task_id, file_path
    download_failed = pyqtSignal(str, str)     # task_id, error_message

    def __init__(self, download_task: DownloadTask, download_path: str, max_retries: int = 3):
        super().__init__()
        self.download_task = download_task
        self.download_path = download_path
        self.max_retries = max_retries
        self.task_id = f"{download_task.session_id}_{download_task.index}"

    def run(self):
        """Thực hiện download với retry logic"""
        for attempt in range(self.max_retries):
            try:
                self.download_task.retry_count = attempt + 1
                self.download_task.status = "downloading"

                # Generate unique filename
                file_path = self.download_task.generate_filename(self.download_path)
                self.download_task.file_path = file_path

                # Download file với progress tracking
                response = requests.get(self.download_task.url, stream=True, timeout=30)
                response.raise_for_status()

                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0

                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)

                            # Update progress
                            if total_size > 0:
                                progress = int((downloaded_size / total_size) * 100)
                                self.download_progress.emit(self.task_id, progress)

                # Download completed
                self.download_task.status = "completed"
                self.download_task.progress = 100
                self.download_completed.emit(self.task_id, file_path)
                return

            except Exception as e:
                error_msg = str(e)
                self.download_task.error_message = error_msg

                if attempt < self.max_retries - 1:
                    # Wait before retry với exponential backoff
                    wait_time = (2 ** attempt) * 1.0
                    time.sleep(wait_time)
                else:
                    # Final failure
                    self.download_task.status = "failed"
                    self.download_failed.emit(self.task_id, error_msg)


class NoWatermarkDownloadWorker(QThread):
    """Worker thread cho download without watermark"""
    download_progress = pyqtSignal(str, int)  # task_id, progress
    download_completed = pyqtSignal(str, str)  # task_id, file_path
    download_failed = pyqtSignal(str, str)     # task_id, error_message

    def __init__(self, cookie: str, work_id: str, download_task: DownloadTask,
                 download_path: str, max_retries: int = 3):
        super().__init__()
        self.cookie = cookie
        self.work_id = work_id
        self.download_task = download_task
        self.download_path = download_path
        self.max_retries = max_retries
        self.task_id = f"{download_task.session_id}_{download_task.index}_no_wm"

    def run(self):
        """Download video without watermark using batch_download_v2 API"""
        for attempt in range(self.max_retries):
            try:
                self.download_task.retry_count = attempt + 1
                self.download_task.status = "downloading"

                # Step 1: Call batch_download_v2 API to get download URL
                download_url = self.get_download_url(with_watermark=False)
                if not download_url:
                    raise Exception("Failed to get no watermark download URL")

                # Step 2: Download file from the URL
                file_path = self.download_task.generate_filename(self.download_path)
                self.download_task.file_path = file_path

                # Download with progress tracking
                response = requests.get(download_url, stream=True, timeout=60)
                response.raise_for_status()

                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0

                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)

                            # Update progress
                            if total_size > 0:
                                progress = int((downloaded_size / total_size) * 100)
                                self.download_progress.emit(self.task_id, progress)

                # Download completed
                self.download_task.status = "completed"
                self.download_task.progress = 100
                self.download_completed.emit(self.task_id, file_path)
                return

            except Exception as e:
                error_msg = str(e)
                self.download_task.error_message = error_msg

                if attempt < self.max_retries - 1:
                    # Wait before retry
                    wait_time = (2 ** attempt) * 1.0
                    time.sleep(wait_time)
                else:
                    # Final failure
                    self.download_task.status = "failed"
                    self.download_failed.emit(self.task_id, error_msg)

    def get_download_url(self, with_watermark: bool = False) -> str:
        """Get download URL from batch_download_v2 API"""
        try:
            mode = "with watermark" if with_watermark else "no watermark"
            print(f"🔍 Getting {mode} URL for work ID: {self.work_id}")

            # Construct API URL
            api_url = "https://api-app-global.klingai.com/api/works/batch_download_v2"

            # Try different work ID formats
            work_id_formats = [
                self.work_id,                    # Original
                str(self.work_id),               # Ensure string
                f'"{self.work_id}"',             # Quoted
                f'[{self.work_id}]',             # Array format
                f'["{self.work_id}"]'            # Quoted array
            ]

            for i, work_id_format in enumerate(work_id_formats):
                print(f"🔄 Trying format {i+1}: {work_id_format}")

                # Parameters - using updated token from working cURL
                params = {
                    '__NS_hxfalcon': 'HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXsMYojNEC2VAlN0_LMZHDhJK9nym8Jrx2cnp4iNdoYGECPbgEPyzfyJ_9LRRA2kT4Wi-fsgpPhcwt79cK7Hy9w4ZK644eMCLcihAJ9lm17QsTGX880s53arOcuxc1MeOcg-45Kx4xpyrjTOt_3Mkr_ImHFudSAZ-FJ7Y-DUacbgvXwTT7HftjkXvbvtUJeS55VYcnIluB1M4K-Uv1gmtWclhEjpbde9eMtPWx05vCI3m11uCAQ4dyGrbRAD2MclQ_kSuxM4i3sMrwRcp-v8N3O4y1TZjuv0k84zX-0WgfvkgoTpOsJ8VA3qdUVEsglNKSEk-4zsj4yb75p6P72qGCI9g2M85afhcA3yOLpxu5Jqu9iIM0wan7w5YySD0-hZCpsJkqjS3BmUi1uqiM9-7-CFHDsZsspqotKT7H6RWq0vpeYZbzEZSKcmhzsW6xywLLMwA6qMtANJxzyg8XApiPFZwkZWBzStdTcEWNK2KC8KvwoUKWWScXFmHyldQ1-v5zUAoXdswP7kYX8sRtP3eYGBzAQrbcZMUumOf_K-z0KVz9Xq6jH98SP2SKHDlUZfHUIaNxNBVjK2z7sPzqBut-_ndDpac7albRp-6CxTQ0rfsSI4dYdi4D6zWpbunkTuq8tx4XP-e5ng4XGiArojq_8hl0x-Y5$HE_6b74ec898bf336f392b721d517a1df58d92120202021e4b88c9a37a901c6cbd8be5ab721bb761efa5b761ec820',
                    'caver': '2',
                    'workIds': work_id_format,
                    'fwm': 'true' if with_watermark else 'false',  # true = with watermark, false = no watermark
                    'fileTypes': 'MP4',
                    'audioTrack': 'false'
                }

                print(f"📡 API URL: {api_url}")
                print(f"📋 Full Params: {params}")
                print(f"🔑 Work ID Format: '{work_id_format}'")

                # Headers
                headers = {
                    'accept': 'application/json, text/plain, */*',
                    'accept-language': 'en',
                    'origin': 'https://app.klingai.com',
                    'priority': 'u=1, i',
                    'referer': 'https://app.klingai.com/',
                    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-site',
                    'time-zone': 'Asia/Saigon',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
                    'cookie': self.cookie
                }

                # Make request
                response = requests.get(api_url, params=params, headers=headers, timeout=30)
                print(f"📊 Response status: {response.status_code}")
                response.raise_for_status()

                # Parse response
                data = response.json()
                print(f"📄 Response data: {data}")

                # Check for success response
                if data.get('result') == 1 and data.get('status') == 200 and 'data' in data:
                    download_data = data['data']

                    # Get cdnUrl from response
                    if isinstance(download_data, dict) and 'cdnUrl' in download_data:
                        url = download_data['cdnUrl']
                        print(f"✅ Got {mode} URL with format {i+1}: {url}")
                        return url

                    # Fallback: check for 'url' field
                    elif isinstance(download_data, dict) and 'url' in download_data:
                        url = download_data['url']
                        print(f"✅ Got {mode} URL (fallback) with format {i+1}: {url}")
                        return url

                print(f"❌ Format {i+1} failed. Status: {data.get('status')}, Message: {data.get('message')}")

                # If this format failed but not the last one, continue to next format
                if i < len(work_id_formats) - 1:
                    continue

            # If all formats failed
            print(f"❌ All work ID formats failed")
            return None

        except Exception as e:
            mode = "with watermark" if with_watermark else "no watermark"
            print(f"❌ Error getting {mode} URL: {e}")
            raise Exception(f"Failed to get {mode} URL: {e}")


class SessionData:
    """Dữ liệu cho một session"""
    def __init__(self, session_id: str, cookie: str):
        self.session_id = session_id
        self.cookie = cookie
        self.image_gen = None
        self.video_gen = None
        self.points = 0.0
        self.is_connected = False
        self.status = "Disconnected"
        self.current_task = None
        self.progress = 0
        self.last_result = None
        self.last_content_type = ""
        self.last_model = "1.0"
        self.last_work_ids = []  # Store work IDs for no watermark download
        self.last_task_id = None  # Store task ID from generation

    def connect(self):
        """Kết nối session với improved error handling"""
        try:
            # Validate cookie format first
            if not self.cookie or len(self.cookie) < 50:
                raise Exception("Invalid cookie format - too short")

            if '=' not in self.cookie:
                raise Exception("Invalid cookie format - missing key=value pairs")

            # Initialize generators
            self.image_gen = ImageGen(self.cookie)
            self.video_gen = VideoGen(self.cookie)

            # Test connection by getting account points
            points = self.image_gen.get_account_point()

            # Validate response
            if points is None:
                raise Exception("API returned None - cookie may be invalid or expired")

            if not isinstance(points, (int, float)):
                raise Exception(f"Unexpected points type: {type(points)}")

            self.points = float(points)
            self.is_connected = True
            self.status = "Connected"
            return True

        except Exception as e:
            error_msg = str(e)

            # Categorize errors for better user understanding
            if "cookie" in error_msg.lower():
                self.status = "Invalid Cookie"
            elif "connection" in error_msg.lower() or "network" in error_msg.lower():
                self.status = "Network Error"
            elif "none" in error_msg.lower() or "api" in error_msg.lower():
                self.status = "API Error"
            else:
                self.status = f"Error: {error_msg[:30]}..."

            self.is_connected = False
            self.image_gen = None
            self.video_gen = None
            self.points = 0.0
            return False

    def validate_connection(self):
        """Validate existing connection"""
        if not self.is_connected or not self.image_gen:
            return False

        try:
            # Quick connection test
            points = self.image_gen.get_account_point()
            if points is None:
                self.is_connected = False
                self.status = "Connection Lost"
                return False

            self.points = float(points)
            return True

        except Exception as e:
            self.is_connected = False
            self.status = f"Connection Lost: {str(e)[:30]}..."
            return False


class UnifiedWorkerThread(QThread):
    """Worker thread cho unified processing"""
    progress_update = pyqtSignal(str, str, int)  # session_id, message, progress
    result_ready = pyqtSignal(str, list, str)    # session_id, results, content_type
    error_occurred = pyqtSignal(str, str)        # session_id, error_message

    def __init__(self, session_data: SessionData, task_type: str, prompt: str,
                 image_path: Optional[str] = None, **kwargs):
        super().__init__()
        self.session_data = session_data
        self.task_type = task_type
        self.prompt = prompt
        self.image_path = image_path
        self.kwargs = kwargs

    def run(self):
        try:
            session_id = self.session_data.session_id
            self.progress_update.emit(session_id, f"Starting {self.task_type} generation...", 10)

            # Validate session connection first
            if not self.session_data.is_connected:
                raise Exception("Session not connected. Please reconnect.")

            # Check if generators are available
            if self.task_type == "image" and not self.session_data.image_gen:
                raise Exception("Image generator not initialized")
            elif self.task_type == "video" and not self.session_data.video_gen:
                raise Exception("Video generator not initialized")

            # Prepare kwargs
            kwargs = self.kwargs.copy()
            if self.image_path:
                kwargs['image_path'] = self.image_path

            self.progress_update.emit(session_id, f"Validating {self.task_type} parameters...", 20)

            # Test connection before generation
            try:
                if self.task_type == "image":
                    # Test image generator connection
                    points = self.session_data.image_gen.get_account_point()
                    if points is None:
                        raise Exception("Cannot get account points - cookie may be invalid")
                else:
                    # Test video generator connection
                    points = self.session_data.video_gen.get_account_point()
                    if points is None:
                        raise Exception("Cannot get account points - cookie may be invalid")

                self.progress_update.emit(session_id, f"Connection verified (Points: {points})", 30)

            except Exception as conn_error:
                raise Exception(f"Connection test failed: {str(conn_error)}")

            # Proceed with generation
            if self.task_type == "image":
                self.progress_update.emit(session_id, "Generating images...", 50)
                results = self.session_data.image_gen.get_images(self.prompt, **kwargs)
            else:  # video
                self.progress_update.emit(session_id, "Generating video...", 50)
                results = self.session_data.video_gen.get_video(self.prompt, **kwargs)

            # Validate results
            if results is None:
                raise Exception("Generation returned None - API may have failed")
            elif not isinstance(results, list):
                raise Exception(f"Unexpected result type: {type(results)}")
            elif len(results) == 0:
                raise Exception("No results generated - check prompt and settings")

            # DISABLED: Work IDs will be handled in on_result_ready instead
            # work_ids = []
            # print(f"🔍 DEBUG: task_type = {self.task_type}")

            if self.task_type == "video":
                # Force check video generator for work IDs
                print(f"🔍 DEBUG: Checking video_gen for work IDs...")
                if hasattr(self.session_data.video_gen, 'last_work_ids'):
                    work_ids = self.session_data.video_gen.last_work_ids
                    print(f"� Retrieved work IDs from video generator: {work_ids}")
                else:
                    print(f"❌ video_gen.last_work_ids not found, checking again...")
                    # Force refresh - sometimes attribute is set after we check
                    import time
                    time.sleep(0.1)  # Small delay
                    if hasattr(self.session_data.video_gen, 'last_work_ids'):
                        work_ids = self.session_data.video_gen.last_work_ids
                        print(f"🔗 Retrieved work IDs after delay: {work_ids}")
                    else:
                        print(f"❌ Still no work IDs found")

            elif self.task_type == "image":
                if hasattr(self.session_data.image_gen, 'last_work_ids'):
                    work_ids = self.session_data.image_gen.last_work_ids
                    print(f"🔗 Retrieved work IDs from image generator: {work_ids}")

            self.progress_update.emit(session_id, "Generation completed!", 100)

            # DISABLED: Work IDs will be handled in on_result_ready instead
            # if work_ids:
            #     self.session_data.last_work_ids = work_ids
            #     print(f"💾 Stored work IDs in session: {work_ids}")
            # else:
            #     print(f"❌ No work IDs to store")

            # Emit results with work IDs
            self.result_ready.emit(session_id, results, self.task_type)

        except Exception as e:
            error_msg = str(e)
            # Add more context to error message
            if "'NoneType' object has no attribute" in error_msg:
                error_msg = f"API Response Error: {error_msg}. Cookie may be invalid or expired."
            elif "Connection" in error_msg:
                error_msg = f"Connection Error: {error_msg}"
            elif "HTTP" in error_msg:
                error_msg = f"Network Error: {error_msg}"

            self.error_occurred.emit(self.session_data.session_id, error_msg)


class CookieManager:
    """Quản lý cookies"""
    
    @staticmethod
    def load_cookies_from_file(file_path: str) -> List[str]:
        """Load cookies từ file txt"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                cookies = [line.strip() for line in f.readlines() if line.strip()]
            return cookies
        except Exception as e:
            raise Exception(f"Error loading cookies: {e}")
    
    @staticmethod
    def validate_cookie(cookie: str) -> bool:
        """Validate cookie format"""
        return len(cookie) > 50 and '=' in cookie
    
    @staticmethod
    def save_cookies_to_file(cookies: List[str], file_path: str):
        """Save cookies to file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for cookie in cookies:
                    f.write(cookie + '\n')
        except Exception as e:
            raise Exception(f"Error saving cookies: {e}")


class PromptManager:
    """Quản lý prompts"""
    
    @staticmethod
    def load_prompts_from_file(file_path: str) -> List[str]:
        """Load prompts từ file txt"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                prompts = [line.strip() for line in f.readlines() if line.strip()]
            return prompts
        except Exception as e:
            raise Exception(f"Error loading prompts: {e}")
    
    @staticmethod
    def get_random_prompt(prompts: List[str]) -> str:
        """Lấy random prompt"""
        return random.choice(prompts) if prompts else ""


class ImageManager:
    """Quản lý images"""
    
    @staticmethod
    def get_supported_formats():
        return ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']
    
    @staticmethod
    def is_valid_image(file_path: str) -> bool:
        """Kiểm tra file có phải ảnh hợp lệ không"""
        if not os.path.exists(file_path):
            return False
        ext = os.path.splitext(file_path)[1].lower()
        return ext in ImageManager.get_supported_formats()
    
    @staticmethod
    def get_images_from_folder(folder_path: str) -> List[str]:
        """Lấy tất cả ảnh từ folder"""
        if not os.path.exists(folder_path):
            return []
        
        images = []
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if os.path.isfile(file_path) and ImageManager.is_valid_image(file_path):
                images.append(file_path)
        return images
    
    @staticmethod
    def get_random_image(folder_path: str) -> Optional[str]:
        """Lấy ngẫu nhiên 1 ảnh từ folder"""
        images = ImageManager.get_images_from_folder(folder_path)
        return random.choice(images) if images else None


class UnifiedMultiSessionGUI(QMainWindow):
    """Unified GUI cho Multi-Session KlingCreator"""

    def __init__(self):
        super().__init__()
        self.sessions: Dict[str, SessionData] = {}
        self.worker_threads: Dict[str, UnifiedWorkerThread] = {}
        self.download_threads: Dict[str, DownloadWorkerThread] = {}
        self.download_tasks: Dict[str, DownloadTask] = {}
        self.prompts_list: List[str] = []
        self.images_folder: str = ""
        self.single_image_path: str = ""

        # Sequential execution control
        self.sequential_mode = True  # Default to sequential
        self.session_queue: List[SessionData] = []
        self.current_session_index = 0
        self.batch_content_type = ""
        self.sequential_timer = QTimer()
        self.sequential_timer.timeout.connect(self.process_next_session)
        self.sequential_delay = 2000  # 2 seconds delay between requests

        self.total_cycles = 1
        self.current_cycle = 0
        self.cycle_sessions_queue = []
        self.cycle_completion_tracker = {}
        self.waiting_for_cycle_completion = False

        # Initialize download settings
        self.download_settings = DownloadSettings()

        self.init_ui()

    def closeEvent(self, event):
        """Handle application close event with proper cleanup"""
        self.log_message("🔄 Shutting down application...")

        # Stop sequential timer
        if hasattr(self, 'sequential_timer'):
            self.sequential_timer.stop()

        # Reset cycle state
        if hasattr(self, 'reset_cycle_state'):
            self.reset_cycle_state()

        # Stop all worker threads
        self.cleanup_worker_threads()

        # Stop all download threads
        self.cleanup_download_threads()

        self.log_message("✅ Application shutdown complete")
        event.accept()

    def cleanup_worker_threads(self):
        """Safely stop all worker threads"""
        if not self.worker_threads:
            return

        self.log_message(f"🔄 Stopping {len(self.worker_threads)} worker threads...")

        for session_id, thread in list(self.worker_threads.items()):
            try:
                if thread.isRunning():
                    self.log_message(f"⏹️ Stopping worker thread for session {session_id}")
                    thread.quit()
                    if not thread.wait(3000):  # Wait up to 3 seconds
                        self.log_message(f"⚠️ Force terminating worker thread for session {session_id}")
                        thread.terminate()
                        thread.wait()
                del self.worker_threads[session_id]
            except Exception as e:
                self.log_message(f"❌ Error stopping worker thread {session_id}: {e}")

        self.worker_threads.clear()

    def cleanup_download_threads(self):
        """Safely stop all download threads"""
        if not self.download_threads:
            return

        self.log_message(f"🔄 Stopping {len(self.download_threads)} download threads...")

        for task_id, thread in list(self.download_threads.items()):
            try:
                if thread.isRunning():
                    self.log_message(f"⏹️ Stopping download thread for task {task_id}")
                    thread.quit()
                    if not thread.wait(3000):  # Wait up to 3 seconds
                        self.log_message(f"⚠️ Force terminating download thread for task {task_id}")
                        thread.terminate()
                        thread.wait()
                del self.download_threads[task_id]
            except Exception as e:
                self.log_message(f"❌ Error stopping download thread {task_id}: {e}")

        self.download_threads.clear()

    def cleanup_finished_worker(self, session_id: str):
        """Clean up finished worker thread"""
        if session_id in self.worker_threads:
            try:
                thread = self.worker_threads[session_id]
                if thread.isFinished():
                    del self.worker_threads[session_id]
                    self.log_message(f"🧹 Cleaned up finished worker thread for session {session_id}")
            except Exception as e:
                self.log_message(f"❌ Error cleaning up worker thread {session_id}: {e}")

    def cleanup_finished_download(self, task_id: str):
        """Clean up finished download thread"""
        if task_id in self.download_threads:
            try:
                thread = self.download_threads[task_id]
                if thread.isFinished():
                    del self.download_threads[task_id]
                    self.log_message(f"🧹 Cleaned up finished download thread for task {task_id}")
            except Exception as e:
                self.log_message(f"❌ Error cleaning up download thread {task_id}: {e}")

    def init_ui(self):
        self.setWindowTitle("KlingCreator Modern - Unified Multi-Session")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1200, 800)

        # Set modern dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #3c3c3c;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #ffffff;
            }
            QPushButton {
                background-color: #4a4a4a;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 4px 8px;
                color: #ffffff;
                font-weight: bold;
                font-size: 11px;
                min-height: 20px;
                max-height: 28px;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
                border-color: #777777;
            }
            QPushButton:pressed {
                background-color: #3a3a3a;
            }
            QLineEdit, QTextEdit {
                background-color: #404040;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 6px;
                color: #ffffff;
            }
            QComboBox {
                background-color: #404040;
                border: 1px solid #666666;
                border-radius: 4px;
                padding: 6px;
                color: #ffffff;
            }
            QTableWidget {
                background-color: #404040;
                alternate-background-color: #4a4a4a;
                gridline-color: #666666;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #5a5a5a;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #666666;
            }
            QTabWidget::pane {
                border: 1px solid #666666;
                background-color: #3c3c3c;
            }
            QTabBar::tab {
                background-color: #4a4a4a;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: #6a6a6a;
            }
        """)

        # Central widget với splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Main splitter (removed header)
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)

        # Left panel - Controls
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)

        # Right panel - Sessions & Results
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)

        # Set splitter proportions
        main_splitter.setSizes([550, 1050])

        # Initialize download all button text
        self.update_download_all_button_text()



    def create_left_panel(self):
        """Create modern left panel with controls"""
        left_widget = QWidget()
        left_widget.setMaximumWidth(600)
        left_widget.setMinimumWidth(500)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(15)

        # Scroll area for better organization
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        scroll_layout.setSpacing(8)  # Reduced spacing for compact layout

        # Cookie Management Section
        cookie_group = self.create_cookie_management_section()
        scroll_layout.addWidget(cookie_group)

        # Image Selection Section
        image_group = self.create_image_selection_section()
        scroll_layout.addWidget(image_group)

        # Prompt Input Section
        prompt_group = self.create_prompt_input_section()
        scroll_layout.addWidget(prompt_group)

        # Generation Controls Section
        generation_group = self.create_generation_controls_section()
        scroll_layout.addWidget(generation_group)

        # Download Settings Section
        download_group = self.create_download_settings_section()
        scroll_layout.addWidget(download_group)

        # Stretch
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_widget)
        left_layout.addWidget(scroll_area)

        return left_widget
        
    def create_cookie_management_section(self):
        """Tạo cookie management section"""
        group = QGroupBox("Cookie Management")
        layout = QVBoxLayout(group)
        layout.setSpacing(6)  # Reduced spacing
        
        input_layout = QHBoxLayout()

        self.manual_cookie_input = QLineEdit()
        self.manual_cookie_input.setPlaceholderText("Paste cookie here...")
        self.manual_cookie_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.manual_cookie_input.setMaximumWidth(250)

        self.add_manual_btn = QPushButton("Add")
        self.add_manual_btn.clicked.connect(self.add_manual_cookie)
        self.add_manual_btn.setMinimumWidth(60)
        self.add_manual_btn.setMaximumWidth(60)

        input_layout.addWidget(QLabel("Manual:"))
        input_layout.addWidget(self.manual_cookie_input)
        input_layout.addWidget(self.add_manual_btn)
        input_layout.addStretch()

        layout.addLayout(input_layout)
        
        file_layout = QHBoxLayout()

        self.cookie_file_path = QLineEdit()
        self.cookie_file_path.setPlaceholderText("Select cookies.txt file...")
        self.cookie_file_path.setReadOnly(True)
        self.cookie_file_path.setMaximumWidth(250)

        self.browse_cookie_file_btn = QPushButton("Browse")
        self.browse_cookie_file_btn.clicked.connect(self.browse_cookie_file)
        self.browse_cookie_file_btn.setMinimumWidth(70)
        self.browse_cookie_file_btn.setMaximumWidth(70)

        self.import_cookies_btn = QPushButton("Import")
        self.import_cookies_btn.clicked.connect(self.import_cookies_from_file)
        self.import_cookies_btn.setMinimumWidth(70)
        self.import_cookies_btn.setMaximumWidth(70)

        file_layout.addWidget(QLabel("File:"))
        file_layout.addWidget(self.cookie_file_path)
        file_layout.addWidget(self.browse_cookie_file_btn)
        file_layout.addWidget(self.import_cookies_btn)
        file_layout.addStretch()

        layout.addLayout(file_layout)
        
        control_layout = QHBoxLayout()
        control_layout.setSpacing(4)

        self.connect_all_btn = QPushButton("Connect All")
        self.connect_all_btn.clicked.connect(self.connect_all_sessions)
        self.connect_all_btn.setMaximumWidth(80)

        self.clear_cookies_btn = QPushButton("Clear All")
        self.clear_cookies_btn.clicked.connect(self.clear_all_cookies)
        self.clear_cookies_btn.setMaximumWidth(70)

        control_layout.addWidget(self.connect_all_btn)
        control_layout.addWidget(self.clear_cookies_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        view_export_layout = QHBoxLayout()
        view_export_layout.setSpacing(4)

        self.view_cookies_btn = QPushButton("View")
        self.view_cookies_btn.clicked.connect(self.view_cookies)
        self.view_cookies_btn.setMaximumWidth(50)

        self.export_cookies_btn = QPushButton("Export")
        self.export_cookies_btn.clicked.connect(self.export_cookies)
        self.export_cookies_btn.setMaximumWidth(55)

        view_export_layout.addWidget(self.view_cookies_btn)
        view_export_layout.addWidget(self.export_cookies_btn)
        view_export_layout.addStretch()

        layout.addLayout(view_export_layout)
        
        # Status
        self.cookie_status_label = QLabel("No cookies loaded")
        layout.addWidget(self.cookie_status_label)
        
        return group

    def create_image_selection_section(self):
        """Tạo image selection section"""
        group = QGroupBox("Image Selection")
        layout = QVBoxLayout(group)

        # Mode selection
        mode_layout = QHBoxLayout()
        self.image_mode_group = QButtonGroup()

        self.single_file_radio = QRadioButton("📄 Single File")
        self.folder_mode_radio = QRadioButton("📁 Random from Folder")
        self.single_file_radio.setChecked(True)

        self.image_mode_group.addButton(self.single_file_radio, 0)
        self.image_mode_group.addButton(self.folder_mode_radio, 1)
        self.image_mode_group.buttonClicked.connect(self.on_image_mode_changed)

        mode_layout.addWidget(self.single_file_radio)
        mode_layout.addWidget(self.folder_mode_radio)
        layout.addLayout(mode_layout)

        self.single_file_layout = QHBoxLayout()
        self.single_file_input = QLineEdit()
        self.single_file_input.setPlaceholderText("Select image file...")
        self.single_file_input.setReadOnly(True)
        self.single_file_input.setMaximumWidth(250)

        self.browse_single_file_btn = QPushButton("Browse")
        self.browse_single_file_btn.clicked.connect(self.browse_single_image_file)
        self.browse_single_file_btn.setMinimumWidth(70)
        self.browse_single_file_btn.setMaximumWidth(70)

        self.single_file_layout.addWidget(self.single_file_input)
        self.single_file_layout.addWidget(self.browse_single_file_btn)
        self.single_file_layout.addStretch()
        layout.addLayout(self.single_file_layout)

        self.folder_layout = QHBoxLayout()
        self.folder_input = QLineEdit()
        self.folder_input.setPlaceholderText("Select images folder...")
        self.folder_input.setReadOnly(True)
        self.folder_input.setMaximumWidth(250)

        self.browse_folder_btn = QPushButton("Browse")
        self.browse_folder_btn.clicked.connect(self.browse_images_folder)
        self.browse_folder_btn.setMinimumWidth(70)
        self.browse_folder_btn.setMaximumWidth(70)

        self.folder_layout.addWidget(self.folder_input)
        self.folder_layout.addWidget(self.browse_folder_btn)
        self.folder_layout.addStretch()
        layout.addLayout(self.folder_layout)

        self.folder_input.setVisible(False)
        self.browse_folder_btn.setVisible(False)

        self.image_preview = QLabel("No image selected")
        self.image_preview.setFixedSize(200, 150)
        self.image_preview.setStyleSheet("border: 1px solid gray;")
        self.image_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_preview.setScaledContents(True)
        layout.addWidget(self.image_preview)

        return group

    def create_prompt_input_section(self):
        """Create modern prompt input section with enhanced file loading"""
        group = QGroupBox("📝 Prompt Input")
        layout = QVBoxLayout(group)
        layout.setSpacing(12)

        # Mode selection with modern styling
        mode_frame = QFrame()
        mode_frame.setStyleSheet("""
            QFrame {
                background-color: #4a4a4a;
                border-radius: 8px;
                padding: 8px;
            }
        """)
        mode_layout = QHBoxLayout(mode_frame)
        self.prompt_mode_group = QButtonGroup()

        self.manual_prompt_radio = QRadioButton("✏️ Manual Input")
        self.file_prompt_radio = QRadioButton("📁 Load from File")
        self.manual_prompt_radio.setChecked(True)

        # Style radio buttons
        radio_style = """
            QRadioButton {
                font-weight: bold;
                color: #ffffff;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #666666;
                border-radius: 8px;
                background-color: #404040;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #4CAF50;
                border-radius: 8px;
                background-color: #4CAF50;
            }
        """
        self.manual_prompt_radio.setStyleSheet(radio_style)
        self.file_prompt_radio.setStyleSheet(radio_style)

        self.prompt_mode_group.addButton(self.manual_prompt_radio, 0)
        self.prompt_mode_group.addButton(self.file_prompt_radio, 1)
        self.prompt_mode_group.buttonClicked.connect(self.on_prompt_mode_changed)

        mode_layout.addWidget(self.manual_prompt_radio)
        mode_layout.addWidget(self.file_prompt_radio)
        mode_layout.addStretch()
        layout.addWidget(mode_frame)

        # Manual prompt input
        self.manual_prompt_input = QTextEdit()
        self.manual_prompt_input.setPlaceholderText("Enter your creative prompt here...\nExample: A beautiful sunset over mountains with flying birds")
        self.manual_prompt_input.setMaximumHeight(100)
        self.manual_prompt_input.setStyleSheet("""
            QTextEdit {
                background-color: #404040;
                border: 2px solid #666666;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #4CAF50;
            }
        """)
        layout.addWidget(self.manual_prompt_input)

        # File-based prompt input with enhanced UI
        self.prompt_file_frame = QFrame()
        self.prompt_file_frame.setStyleSheet("""
            QFrame {
                background-color: #4a4a4a;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        file_layout = QVBoxLayout(self.prompt_file_frame)

        # File selection row - responsive
        file_select_layout = QHBoxLayout()
        self.prompt_file_input = QLineEdit()
        self.prompt_file_input.setPlaceholderText("📄 Select prompts.txt file...")
        self.prompt_file_input.setReadOnly(True)
        self.prompt_file_input.setMaximumWidth(250)  # Limit width
        self.prompt_file_input.setStyleSheet("""
            QLineEdit {
                background-color: #505050;
                border: 1px solid #666666;
                border-radius: 6px;
                padding: 8px;
                font-size: 11px;
            }
        """)

        self.browse_prompt_file_btn = QPushButton("Browse")
        self.browse_prompt_file_btn.clicked.connect(self.browse_prompt_file)
        self.browse_prompt_file_btn.setMinimumWidth(70)  # Ensure visibility
        self.browse_prompt_file_btn.setMaximumWidth(70)
        self.browse_prompt_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #5a5a5a;
                border: 1px solid #777777;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6a6a6a;
            }
        """)

        file_select_layout.addWidget(self.prompt_file_input)
        file_select_layout.addWidget(self.browse_prompt_file_btn)
        file_select_layout.addStretch()  # Add stretch
        file_layout.addLayout(file_select_layout)

        # Load button and status
        load_layout = QHBoxLayout()
        self.load_prompts_btn = QPushButton("🔄 Load Prompts")
        self.load_prompts_btn.clicked.connect(self.load_prompts_from_file)
        self.load_prompts_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #666666;
                color: #999999;
            }
        """)

        load_layout.addWidget(self.load_prompts_btn)
        load_layout.addStretch()
        file_layout.addLayout(load_layout)

        layout.addWidget(self.prompt_file_frame)

        # Initially hide file controls
        self.prompt_file_frame.setVisible(False)

        # Prompt status with modern styling
        self.prompt_status_label = QLabel("✏️ Manual mode - enter prompt above")
        self.prompt_status_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-weight: bold;
                padding: 8px;
                background-color: #3a3a3a;
                border-radius: 6px;
                border-left: 4px solid #4CAF50;
            }
        """)
        layout.addWidget(self.prompt_status_label)

        return group

    def create_generation_controls_section(self):
        """Tạo generation controls section"""
        group = QGroupBox("Generation Controls")
        layout = QVBoxLayout(group)

        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Type:"))

        self.content_type_combo = QComboBox()
        self.content_type_combo.addItems(["image", "video"])
        self.content_type_combo.setMaximumWidth(100)
        type_layout.addWidget(self.content_type_combo)
        type_layout.addStretch()

        layout.addLayout(type_layout)

        execution_layout = QHBoxLayout()
        execution_layout.addWidget(QLabel("Execution Mode:"))

        self.execution_mode_combo = QComboBox()
        self.execution_mode_combo.addItems(["🔄 Sequential with Delay", "⚡ Parallel (All at once)"])
        self.execution_mode_combo.setCurrentIndex(0)
        self.execution_mode_combo.currentTextChanged.connect(self.on_execution_mode_changed)
        self.execution_mode_combo.setToolTip(
            f"Sequential: Send requests with {self.sequential_delay/1000}s delay between each\n"
            "Parallel: Send all requests simultaneously (may overload server)\n\n"
            "Sequential prevents API overload by spacing out requests"
        )
        execution_layout.addWidget(self.execution_mode_combo)
        execution_layout.addStretch()

        layout.addLayout(execution_layout)

        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("Sequential Delay:"))

        self.delay_input = QLineEdit()
        self.delay_input.setText("2")
        self.delay_input.setMaximumWidth(60)
        self.delay_input.setToolTip("Time delay in seconds between sequential API requests (e.g., 1.5, 3, 10)")
        self.delay_input.textChanged.connect(self.on_delay_input_changed)

        delay_layout.addWidget(self.delay_input)
        delay_layout.addWidget(QLabel("seconds"))
        delay_layout.addStretch()
        layout.addLayout(delay_layout)

        cycle_layout = QHBoxLayout()
        cycle_layout.addWidget(QLabel("Generation Cycles:"))

        self.cycle_input = QLineEdit()
        self.cycle_input.setText("1")
        self.cycle_input.setMaximumWidth(60)
        self.cycle_input.setToolTip("Number of times to repeat the entire generation process (e.g., 1, 3, 5)")
        self.cycle_input.textChanged.connect(self.on_cycles_input_changed)

        cycle_layout.addWidget(self.cycle_input)
        cycle_layout.addWidget(QLabel("cycles"))
        cycle_layout.addStretch()
        layout.addLayout(cycle_layout)

        # Video options
        self.video_options_group = QGroupBox("Video Options")
        video_layout = QGridLayout(self.video_options_group)

        self.high_quality_cb = QCheckBox("High Quality")
        self.auto_extend_cb = QCheckBox("Auto Extend")

        self.model_combo = QComboBox()
        self.model_combo.addItems(["1.0", "1.5", "1.6"])
        self.model_combo.setCurrentText("1.6")

        video_layout.addWidget(self.high_quality_cb, 0, 0)
        video_layout.addWidget(self.auto_extend_cb, 0, 1)
        video_layout.addWidget(QLabel("Model:"), 1, 0)
        video_layout.addWidget(self.model_combo, 1, 1)

        layout.addWidget(self.video_options_group)

        # Execute button
        self.execute_btn = QPushButton("🔄 Execute Sequential")
        self.execute_btn.clicked.connect(self.execute_batch_generation)
        self.execute_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; font-size: 14px; }")
        self.execute_btn.setToolTip("Execute sessions one by one (safer for API)")
        layout.addWidget(self.execute_btn)

        return group

    def create_download_settings_section(self):
        """Tạo download settings section"""
        group = QGroupBox("Download Settings")
        layout = QVBoxLayout(group)

        # Auto download toggle
        self.auto_download_cb = QCheckBox("Enable Automatic Download")
        self.auto_download_cb.setChecked(self.download_settings.auto_download_enabled)
        self.auto_download_cb.stateChanged.connect(self.on_auto_download_changed)
        layout.addWidget(self.auto_download_cb)

        # Watermark mode selection - responsive
        watermark_layout = QHBoxLayout()
        watermark_layout.addWidget(QLabel("Download Mode:"))

        self.watermark_mode_combo = QComboBox()
        self.watermark_mode_combo.addItems(["With Watermark", "No Watermark"])
        self.watermark_mode_combo.setMaximumWidth(150)  # Limit width
        # Set initial value based on saved settings
        initial_index = 1 if self.download_settings.no_watermark_mode else 0
        self.watermark_mode_combo.setCurrentIndex(initial_index)
        self.watermark_mode_combo.currentTextChanged.connect(self.on_watermark_mode_changed)

        watermark_layout.addWidget(self.watermark_mode_combo)
        watermark_layout.addStretch()  # Push to left
        layout.addLayout(watermark_layout)

        # Download path selection - responsive
        path_layout = QHBoxLayout()

        self.download_path_input = QLineEdit()
        self.download_path_input.setText(self.download_settings.download_path)
        self.download_path_input.setReadOnly(True)
        self.download_path_input.setMaximumWidth(250)  # Limit width

        self.browse_download_path_btn = QPushButton("Browse")
        self.browse_download_path_btn.clicked.connect(self.browse_download_path)
        self.browse_download_path_btn.setMinimumWidth(70)  # Ensure visibility
        self.browse_download_path_btn.setMaximumWidth(70)

        self.open_download_folder_btn = QPushButton("Open")
        self.open_download_folder_btn.clicked.connect(self.open_download_folder)
        self.open_download_folder_btn.setMinimumWidth(60)  # Ensure visibility
        self.open_download_folder_btn.setMaximumWidth(60)

        path_layout.addWidget(QLabel("Path:"))
        path_layout.addWidget(self.download_path_input)
        path_layout.addWidget(self.browse_download_path_btn)
        path_layout.addWidget(self.open_download_folder_btn)
        path_layout.addStretch()  # Push to left

        layout.addLayout(path_layout)

        # Download status
        self.download_status_label = QLabel("Ready")
        self.download_status_label.setStyleSheet("color: green;")
        layout.addWidget(self.download_status_label)

        # Validate initial path
        self.validate_download_path()

        return group

    def create_right_panel(self):
        """Tạo right panel với sessions và results"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Tab widget
        self.tab_widget = QTabWidget()

        # Sessions tab
        sessions_tab = self.create_sessions_tab()
        self.tab_widget.addTab(sessions_tab, "Sessions Status")

        # Results tab
        results_tab = self.create_results_tab()
        self.tab_widget.addTab(results_tab, "Results")

        # Logs tab
        logs_tab = self.create_logs_tab()
        self.tab_widget.addTab(logs_tab, "Logs")

        right_layout.addWidget(self.tab_widget)

        return right_widget

    def create_sessions_tab(self):
        """Tạo sessions status tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Sessions table
        self.sessions_table = QTableWidget()
        self.sessions_table.setColumnCount(5)
        self.sessions_table.setHorizontalHeaderLabels([
            "Session ID", "Status", "Points", "Progress", "Current Task"
        ])

        # Auto resize columns
        header = self.sessions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)

        # Enable context menu
        self.sessions_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.sessions_table.customContextMenuRequested.connect(self.show_session_context_menu)

        layout.addWidget(self.sessions_table)

        return tab

    def create_results_tab(self):
        """Tạo results tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Results area
        self.results_area = QScrollArea()
        self.results_widget = QWidget()
        self.results_layout = QVBoxLayout(self.results_widget)
        self.results_area.setWidget(self.results_widget)
        self.results_area.setWidgetResizable(True)

        layout.addWidget(self.results_area)

        # Download all button - compact
        self.download_all_btn = QPushButton("Download All")
        self.download_all_btn.clicked.connect(self.download_all_results)
        self.download_all_btn.setEnabled(False)
        self.download_all_btn.setMaximumHeight(32)
        layout.addWidget(self.download_all_btn)

        return tab

    def create_logs_tab(self):
        """Tạo logs tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Logs area
        self.logs_area = QTextEdit()
        self.logs_area.setReadOnly(True)
        layout.addWidget(self.logs_area)

        # Clear logs button
        self.clear_logs_btn = QPushButton("Clear Logs")
        self.clear_logs_btn.clicked.connect(self.clear_logs)
        layout.addWidget(self.clear_logs_btn)

        return tab

    # Event handlers
    def on_image_mode_changed(self):
        """Xử lý thay đổi image mode"""
        if self.single_file_radio.isChecked():
            # Show single file controls
            self.single_file_input.setVisible(True)
            self.browse_single_file_btn.setVisible(True)
            # Hide folder controls
            self.folder_input.setVisible(False)
            self.browse_folder_btn.setVisible(False)
        else:
            # Hide single file controls
            self.single_file_input.setVisible(False)
            self.browse_single_file_btn.setVisible(False)
            # Show folder controls
            self.folder_input.setVisible(True)
            self.browse_folder_btn.setVisible(True)

    def on_prompt_mode_changed(self):
        """Handle prompt mode changes with modern UI updates"""
        if self.manual_prompt_radio.isChecked():
            # Show manual input
            self.manual_prompt_input.setVisible(True)
            # Hide file controls
            self.prompt_file_frame.setVisible(False)
            self.prompt_status_label.setText("✏️ Manual mode - enter prompt above")
            self.prompt_status_label.setStyleSheet("""
                QLabel {
                    color: #4CAF50;
                    font-weight: bold;
                    padding: 8px;
                    background-color: #3a3a3a;
                    border-radius: 6px;
                    border-left: 4px solid #4CAF50;
                }
            """)
        else:
            # Hide manual input
            self.manual_prompt_input.setVisible(False)
            # Show file controls
            self.prompt_file_frame.setVisible(True)
            prompts_count = len(self.prompts_list) if self.prompts_list else 0
            if prompts_count > 0:
                self.prompt_status_label.setText(f"📁 File mode - {prompts_count} prompts loaded")
                self.prompt_status_label.setStyleSheet("""
                    QLabel {
                        color: #2196F3;
                        font-weight: bold;
                        padding: 8px;
                        background-color: #3a3a3a;
                        border-radius: 6px;
                        border-left: 4px solid #2196F3;
                    }
                """)
            else:
                self.prompt_status_label.setText("📁 File mode - load prompts from file")
                self.prompt_status_label.setStyleSheet("""
                    QLabel {
                        color: #FF9800;
                        font-weight: bold;
                        padding: 8px;
                        background-color: #3a3a3a;
                        border-radius: 6px;
                        border-left: 4px solid #FF9800;
                    }
                """)

    def on_execution_mode_changed(self, text):
        """Handle execution mode change"""
        self.sequential_mode = "Sequential" in text
        mode = "sequential API calls" if self.sequential_mode else "parallel API calls"
        self.log_message(f"⚙️ Execution mode changed to: {mode}")

        # Update execute button text
        if self.sequential_mode:
            self.execute_btn.setText("🔄 Execute Sequential API")
            self.execute_btn.setToolTip(f"Send API requests with {self.sequential_delay/1000}s delay between each")
        else:
            self.execute_btn.setText("⚡ Execute Parallel API")
            self.execute_btn.setToolTip("Send all API requests simultaneously")

    def on_delay_input_changed(self, text):
        """Handle custom delay input change"""
        try:
            delay_seconds = float(text) if text else 2.0
            # Validate range (0.1 to 60 seconds)
            delay_seconds = max(0.1, min(60.0, delay_seconds))
            self.sequential_delay = int(delay_seconds * 1000)  # Convert to milliseconds
            self.log_message(f"⚙️ Sequential delay changed to: {delay_seconds}s")

            # Update tooltip
            if self.sequential_mode:
                self.execute_btn.setToolTip(f"Send API requests with {delay_seconds}s delay between each")
        except ValueError:
            # Invalid input, keep current delay
            pass

    def on_cycles_input_changed(self, text):
        """Handle custom cycles input change"""
        try:
            cycles = int(text) if text else 1
            # Validate range (1 to 20 cycles)
            cycles = max(1, min(20, cycles))
            self.total_cycles = cycles
            self.log_message(f"⚙️ Generation cycles set to: {cycles}")

            # Update execute button tooltip
            if cycles > 1:
                cycle_info = f" (will repeat {cycles} times)"
                if self.sequential_mode:
                    self.execute_btn.setToolTip(f"Send API requests with {self.sequential_delay/1000}s delay between each{cycle_info}")
                else:
                    self.execute_btn.setToolTip(f"Send all API requests simultaneously{cycle_info}")
        except ValueError:
            # Invalid input, keep current cycles
            pass

    def process_next_session(self):
        """Process next session in sequential mode with cycle support"""
        if self.current_session_index < len(self.session_queue):
            session_data = self.session_queue[self.current_session_index]
            cycle_info = f" (Cycle {self.current_cycle}/{self.total_cycles})" if self.total_cycles > 1 else ""
            self.log_message(f"🔄 Sequential: Sending request {self.current_session_index + 1}/{len(self.session_queue)} (Session: {session_data.session_id}){cycle_info}")
            self.start_session_generation(session_data, self.batch_content_type)

            self.current_session_index += 1

            # Schedule next session if available
            if self.current_session_index < len(self.session_queue):
                self.sequential_timer.start(self.sequential_delay)
            else:
                # Current cycle requests sent, now wait for completion
                self.log_message(f"🎉 Sequential: Cycle {self.current_cycle}/{self.total_cycles} - All {len(self.session_queue)} requests sent")

                if self.total_cycles > 1:
                    # Set waiting flag for multi-cycle mode
                    self.waiting_for_cycle_completion = True
                    self.log_message(f"⏳ Waiting for Cycle {self.current_cycle}/{self.total_cycles} to complete (generation + download)...")
                    self.log_message(f"🔍 Debug: Current tracker state: {self.cycle_completion_tracker}")
                else:
                    # Single cycle, reset state
                    self.reset_cycle_state()
        else:
            # Stop timer if no more sessions
            self.sequential_timer.stop()

    def check_cycle_completion(self, session_id):
        """Check if current cycle is complete and start next cycle if needed"""
        self.log_message(f"🔍 Debug: Checking cycle completion for {session_id}")
        self.log_message(f"🔍 Debug: waiting_for_cycle_completion = {self.waiting_for_cycle_completion}")
        self.log_message(f"🔍 Debug: cycle_completion_tracker = {self.cycle_completion_tracker}")

        if not self.waiting_for_cycle_completion or not self.cycle_completion_tracker:
            self.log_message(f"🔍 Debug: Early return - not waiting or no tracker")
            return

        # Check if all sessions in current cycle are complete
        all_complete = True
        for sid, status in self.cycle_completion_tracker.items():
            if not (status['generation_complete'] and status['download_complete']):
                all_complete = False
                self.log_message(f"🔍 Debug: {sid} not complete - gen:{status['generation_complete']}, dl:{status['download_complete']}")
                break

        self.log_message(f"🔍 Debug: all_complete = {all_complete}")

        if all_complete:
            self.log_message(f"✅ Cycle {self.current_cycle}/{self.total_cycles} fully completed (generation + download)")
            self.waiting_for_cycle_completion = False

            # Check if we need to start next cycle
            if self.current_cycle < self.total_cycles:
                self.log_message(f"🔍 Debug: Starting next cycle {self.current_cycle + 1}/{self.total_cycles}")
                self.start_next_cycle()
            else:
                # All cycles completed
                self.log_message(f"🎉 All {self.total_cycles} cycles completed! Total requests: {self.total_cycles * len(self.cycle_sessions_queue)}")
                self.reset_cycle_state()

    def start_next_cycle(self):
        """Start the next cycle after current cycle completion"""
        self.current_cycle += 1
        self.current_session_index = 0
        self.session_queue = self.cycle_sessions_queue.copy()

        self.log_message(f"🔄 Starting Cycle {self.current_cycle}/{self.total_cycles}")

        # Start first session of next cycle immediately
        if self.session_queue:
            session_data = self.session_queue[0]
            cycle_info = f" (Cycle {self.current_cycle}/{self.total_cycles})"
            self.log_message(f"🔄 Sequential: Sending request 1/{len(self.session_queue)} (Session: {session_data.session_id}){cycle_info}")
            self.start_session_generation(session_data, self.batch_content_type)
            self.current_session_index = 1

            # Schedule remaining sessions
            if len(self.session_queue) > 1:
                self.sequential_timer.start(self.sequential_delay)

    def reset_cycle_state(self):
        """Reset all cycle-related state"""
        self.session_queue.clear()
        self.cycle_sessions_queue.clear()
        self.cycle_completion_tracker.clear()
        self.current_session_index = 0
        self.current_cycle = 0
        self.waiting_for_cycle_completion = False

    # Download settings methods
    def on_auto_download_changed(self, state):
        """Xử lý thay đổi auto download setting"""
        self.download_settings.auto_download_enabled = state == Qt.CheckState.Checked
        self.download_settings.save_settings()
        status = "enabled" if self.download_settings.auto_download_enabled else "disabled"
        self.log_message(f"⚙️ Auto download {status}")

    def on_watermark_mode_changed(self, text):
        """Xử lý thay đổi watermark mode setting"""
        self.download_settings.no_watermark_mode = (text == "No Watermark")
        self.download_settings.save_settings()
        mode = "no watermark" if self.download_settings.no_watermark_mode else "with watermark"
        self.log_message(f"⚙️ Download mode changed to: {mode}")

        # Update download all button text
        self.update_download_all_button_text()

        # Refresh results display to update button styles
        self.refresh_results_display()

    def update_download_all_button_text(self):
        """Update download all button text based on current mode"""
        if self.download_settings.no_watermark_mode:
            self.download_all_btn.setText("Download All (No WM)")
        else:
            self.download_all_btn.setText("Download All")

    def refresh_results_display(self):
        """Refresh the results display to update button styles"""
        # Trigger a refresh of the results display
        for session_id, session_data in self.sessions.items():
            if session_data.last_result:
                self.update_sessions_display()

    def browse_download_path(self):
        """Browse download path"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "Select Download Directory", self.download_settings.download_path
        )
        if folder_path:
            self.download_settings.download_path = folder_path
            self.download_path_input.setText(folder_path)
            self.download_settings.save_settings()
            self.validate_download_path()
            self.log_message(f"📁 Download path changed to: {folder_path}")

    def validate_download_path(self):
        """Validate download path"""
        try:
            if self.download_settings.ensure_download_directory():
                self.download_status_label.setText("✅ Path valid and writable")
                self.download_status_label.setStyleSheet("color: green;")
                return True
            else:
                self.download_status_label.setText("❌ Path invalid or not writable")
                self.download_status_label.setStyleSheet("color: red;")
                return False
        except Exception as e:
            self.download_status_label.setText(f"❌ Error: {str(e)[:30]}...")
            self.download_status_label.setStyleSheet("color: red;")
            return False

    def open_download_folder(self):
        """Mở download folder"""
        try:
            if os.path.exists(self.download_settings.download_path):
                if platform.system() == "Windows":
                    os.startfile(self.download_settings.download_path)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", self.download_settings.download_path])
                else:  # Linux
                    subprocess.run(["xdg-open", self.download_settings.download_path])
                self.log_message(f"📂 Opened download folder: {self.download_settings.download_path}")
            else:
                QMessageBox.warning(self, "Warning", "Download folder does not exist!")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open folder: {e}")
            self.log_message(f"❌ Failed to open download folder: {e}")

    # Cookie management methods
    def add_manual_cookie(self):
        """Thêm cookie manual"""
        cookie = self.manual_cookie_input.text().strip()
        if not cookie:
            QMessageBox.warning(self, "Warning", "Please enter a cookie!")
            return

        if not CookieManager.validate_cookie(cookie):
            QMessageBox.warning(self, "Warning", "Invalid cookie format!")
            return

        # Add to sessions
        session_id = str(uuid.uuid4())[:8]
        session_data = SessionData(session_id, cookie)
        self.sessions[session_id] = session_data

        self.manual_cookie_input.clear()
        self.update_sessions_display()
        self.log_message(f"✅ Added session {session_id}")

    def browse_cookie_file(self):
        """Browse cookie file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Cookies File", "", "Text files (*.txt)"
        )
        if file_path:
            self.cookie_file_path.setText(file_path)

    def import_cookies_from_file(self):
        """Import cookies từ file"""
        file_path = self.cookie_file_path.text().strip()
        if not file_path:
            QMessageBox.warning(self, "Warning", "Please select a cookies file!")
            return

        try:
            cookies = CookieManager.load_cookies_from_file(file_path)
            added_count = 0

            for cookie in cookies:
                if CookieManager.validate_cookie(cookie):
                    session_id = str(uuid.uuid4())[:8]
                    session_data = SessionData(session_id, cookie)
                    self.sessions[session_id] = session_data
                    added_count += 1

            self.update_sessions_display()
            self.log_message(f"✅ Imported {added_count} cookies from file")
            QMessageBox.information(self, "Success", f"Imported {added_count} valid cookies!")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to import cookies: {e}")
            self.log_message(f"❌ Error importing cookies: {e}")

    def connect_all_sessions(self):
        """Kết nối tất cả sessions với improved error reporting"""
        if not self.sessions:
            QMessageBox.warning(self, "Warning", "No sessions to connect!")
            return

        connected_count = 0
        failed_sessions = []

        for session_id, session_data in self.sessions.items():
            self.log_message(f"🔗 Connecting session {session_id}...")

            if session_data.connect():
                connected_count += 1
                self.log_message(f"✅ Connected session {session_id} - Points: {session_data.points}")
            else:
                failed_sessions.append((session_id, session_data.status))
                self.log_message(f"❌ Failed to connect session {session_id}: {session_data.status}")

        self.update_sessions_display()

        # Show detailed results
        result_msg = f"Connected {connected_count}/{len(self.sessions)} sessions"
        if failed_sessions:
            result_msg += "\n\nFailed sessions:"
            for session_id, error in failed_sessions:
                result_msg += f"\n• {session_id}: {error}"
            result_msg += "\n\nTip: Check cookie validity and network connection"

        QMessageBox.information(self, "Connection Results", result_msg)

    def clear_all_cookies(self):
        """Xóa tất cả cookies"""
        reply = QMessageBox.question(self, "Confirm", "Clear all cookies and sessions?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.sessions.clear()
            self.worker_threads.clear()
            self.update_sessions_display()
            self.log_message("🗑️ Cleared all cookies and sessions")

    def view_cookies(self):
        """View tất cả cookies đã add"""
        if not self.sessions:
            QMessageBox.information(self, "No Cookies", "No cookies have been added yet.")
            return

        # Tạo dialog để hiển thị cookies
        dialog = QDialog(self)
        dialog.setWindowTitle("View Cookies")
        dialog.setModal(True)
        dialog.resize(800, 600)

        layout = QVBoxLayout(dialog)

        # Header
        header_label = QLabel(f"📋 Total Cookies: {len(self.sessions)}")
        header_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(header_label)

        # Table để hiển thị cookies
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["Session ID", "Status", "Points", "Cookie Preview", "Actions"])
        table.setRowCount(len(self.sessions))

        # Header resize
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 100)  # Session ID
        header.resizeSection(1, 100)  # Status
        header.resizeSection(2, 80)   # Points
        header.resizeSection(3, 300)  # Cookie Preview

        # Populate table
        for row, (session_id, session_data) in enumerate(self.sessions.items()):
            # Session ID
            table.setItem(row, 0, QTableWidgetItem(session_id))

            # Status
            status_item = QTableWidgetItem(session_data.status)
            if session_data.is_connected:
                status_item.setBackground(QColor(144, 238, 144))  # Light green
            else:
                status_item.setBackground(QColor(255, 182, 193))  # Light red
            table.setItem(row, 1, status_item)

            # Points
            points_text = str(session_data.points) if session_data.points else "N/A"
            table.setItem(row, 2, QTableWidgetItem(points_text))

            # Cookie Preview (first 50 chars + ...)
            cookie_preview = session_data.cookie[:50] + "..." if len(session_data.cookie) > 50 else session_data.cookie
            table.setItem(row, 3, QTableWidgetItem(cookie_preview))

            # Actions button
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 2, 5, 2)

            # Copy full cookie button
            copy_btn = QPushButton("Copy")
            copy_btn.setMaximumWidth(60)
            copy_btn.clicked.connect(lambda checked=False, cookie=session_data.cookie: self.copy_cookie_to_clipboard(cookie))

            # Remove cookie button
            remove_btn = QPushButton("Remove")
            remove_btn.setMaximumWidth(70)
            remove_btn.clicked.connect(lambda checked=False, sid=session_id: self.remove_single_cookie(sid, dialog))

            actions_layout.addWidget(copy_btn)
            actions_layout.addWidget(remove_btn)

            table.setCellWidget(row, 4, actions_widget)

        layout.addWidget(table)

        # Bottom buttons
        button_layout = QHBoxLayout()

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(lambda: self.refresh_cookie_view(table))

        export_btn = QPushButton("Export All")
        export_btn.clicked.connect(lambda: self.export_cookies())

        close_btn = QPushButton("Close")
        close_btn.clicked.connect(dialog.close)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(export_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        dialog.exec()

    def copy_cookie_to_clipboard(self, cookie: str):
        """Copy cookie to clipboard"""
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(cookie)
            self.log_message("📋 Cookie copied to clipboard")
            QMessageBox.information(self, "Success", "Cookie copied to clipboard!")
        except Exception as e:
            self.log_message(f"❌ Failed to copy cookie: {e}")
            QMessageBox.warning(self, "Error", f"Failed to copy cookie: {e}")

    def remove_single_cookie(self, session_id: str, dialog):
        """Remove single cookie"""
        reply = QMessageBox.question(dialog, "Confirm",
                                   f"Remove cookie for session {session_id}?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            if session_id in self.sessions:
                del self.sessions[session_id]
                self.update_sessions_display()
                self.log_message(f"🗑️ Removed cookie for session {session_id}")
                dialog.close()  # Close dialog to refresh
                self.view_cookies()  # Reopen with updated data

    def refresh_cookie_view(self, table):
        """Refresh cookie view table"""
        # Update table data
        table.setRowCount(len(self.sessions))

        for row, (session_id, session_data) in enumerate(self.sessions.items()):
            # Update status
            status_item = QTableWidgetItem(session_data.status)
            if session_data.is_connected:
                status_item.setBackground(QColor(144, 238, 144))
            else:
                status_item.setBackground(QColor(255, 182, 193))
            table.setItem(row, 1, status_item)

            # Update points
            points_text = str(session_data.points) if session_data.points else "N/A"
            table.setItem(row, 2, QTableWidgetItem(points_text))

        self.log_message("🔄 Cookie view refreshed")

    def export_cookies(self):
        """Export tất cả cookies ra file"""
        if not self.sessions:
            QMessageBox.information(self, "No Cookies", "No cookies to export.")
            return

        # Choose export file
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Cookies", "exported_cookies.txt",
            "Text files (*.txt);;All files (*.*)"
        )

        if not file_path:
            return

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("# Exported Cookies from Unified Multi-Session KlingCreator\n")
                f.write(f"# Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Total Cookies: {len(self.sessions)}\n")
                f.write("# Format: One cookie per line\n")
                f.write("# Lines starting with # are comments\n\n")

                for session_id, session_data in self.sessions.items():
                    f.write(f"# Session ID: {session_id}\n")
                    f.write(f"# Status: {session_data.status}\n")
                    f.write(f"# Points: {session_data.points}\n")
                    f.write(f"{session_data.cookie}\n\n")

            self.log_message(f"📤 Exported {len(self.sessions)} cookies to {file_path}")
            QMessageBox.information(self, "Export Success",
                                  f"Successfully exported {len(self.sessions)} cookies to:\n{file_path}")

        except Exception as e:
            self.log_message(f"❌ Export failed: {e}")
            QMessageBox.critical(self, "Export Error", f"Failed to export cookies:\n{e}")

    def show_session_context_menu(self, position):
        """Hiển thị context menu khi right-click trên sessions table"""
        item = self.sessions_table.itemAt(position)
        if item is None:
            return

        # Get session ID từ row được click
        row = item.row()
        session_id_item = self.sessions_table.item(row, 0)
        if not session_id_item:
            return

        session_id = session_id_item.text()
        if session_id not in self.sessions:
            return

        session_data = self.sessions[session_id]

        # Tạo context menu
        context_menu = QMenu(self)

        # View Cookie action
        view_action = QAction("🔍 View Cookie", self)
        view_action.triggered.connect(lambda: self.view_single_cookie(session_id))
        context_menu.addAction(view_action)

        # Edit Cookie action
        edit_action = QAction("✏️ Edit Cookie", self)
        edit_action.triggered.connect(lambda: self.edit_single_cookie(session_id))
        context_menu.addAction(edit_action)

        context_menu.addSeparator()

        # Copy Cookie action
        copy_action = QAction("📋 Copy Cookie", self)
        copy_action.triggered.connect(lambda: self.copy_cookie_to_clipboard(session_data.cookie))
        context_menu.addAction(copy_action)

        # Reconnect action
        reconnect_action = QAction("🔄 Reconnect", self)
        reconnect_action.triggered.connect(lambda: self.reconnect_single_session(session_id))
        context_menu.addAction(reconnect_action)

        context_menu.addSeparator()

        # Remove Cookie action
        remove_action = QAction("🗑️ Remove Cookie", self)
        remove_action.triggered.connect(lambda: self.remove_single_cookie_from_context(session_id))
        context_menu.addAction(remove_action)

        # Show context menu
        context_menu.exec(self.sessions_table.mapToGlobal(position))

    def view_single_cookie(self, session_id: str):
        """View single cookie trong dialog"""
        if session_id not in self.sessions:
            return

        session_data = self.sessions[session_id]

        # Tạo dialog để hiển thị cookie
        dialog = QDialog(self)
        dialog.setWindowTitle(f"View Cookie - {session_id}")
        dialog.setModal(True)
        dialog.resize(600, 400)

        layout = QVBoxLayout(dialog)

        # Header info
        info_layout = QGridLayout()

        info_layout.addWidget(QLabel("Session ID:"), 0, 0)
        info_layout.addWidget(QLabel(session_id), 0, 1)

        info_layout.addWidget(QLabel("Status:"), 1, 0)
        status_label = QLabel(session_data.status)
        if session_data.is_connected:
            status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            status_label.setStyleSheet("color: red; font-weight: bold;")
        info_layout.addWidget(status_label, 1, 1)

        info_layout.addWidget(QLabel("Points:"), 2, 0)
        points_text = str(session_data.points) if session_data.points else "N/A"
        info_layout.addWidget(QLabel(points_text), 2, 1)

        layout.addLayout(info_layout)

        # Cookie content
        layout.addWidget(QLabel("Cookie Content:"))
        cookie_text = QTextEdit()
        cookie_text.setPlainText(session_data.cookie)
        cookie_text.setReadOnly(True)
        cookie_text.setFont(QFont("Courier", 9))
        layout.addWidget(cookie_text)

        # Buttons
        button_layout = QHBoxLayout()

        copy_btn = QPushButton("Copy to Clipboard")
        copy_btn.clicked.connect(lambda: self.copy_cookie_to_clipboard(session_data.cookie))

        edit_btn = QPushButton("Edit Cookie")
        edit_btn.clicked.connect(lambda: (dialog.close(), self.edit_single_cookie(session_id)))

        close_btn = QPushButton("Close")
        close_btn.clicked.connect(dialog.close)

        button_layout.addWidget(copy_btn)
        button_layout.addWidget(edit_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        dialog.exec()

    def edit_single_cookie(self, session_id: str):
        """Edit single cookie"""
        if session_id not in self.sessions:
            return

        session_data = self.sessions[session_id]

        # Tạo dialog để edit cookie
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Edit Cookie - {session_id}")
        dialog.setModal(True)
        dialog.resize(600, 400)

        layout = QVBoxLayout(dialog)

        # Header
        layout.addWidget(QLabel(f"Editing cookie for session: {session_id}"))

        # Cookie editor
        layout.addWidget(QLabel("Cookie Content:"))
        cookie_editor = QTextEdit()
        cookie_editor.setPlainText(session_data.cookie)
        cookie_editor.setFont(QFont("Courier", 9))
        layout.addWidget(cookie_editor)

        # Warning
        warning_label = QLabel("⚠️ Warning: Editing cookie will disconnect current session")
        warning_label.setStyleSheet("color: orange; font-weight: bold;")
        layout.addWidget(warning_label)

        # Buttons
        button_layout = QHBoxLayout()

        save_btn = QPushButton("Save & Reconnect")
        save_btn.clicked.connect(lambda: self.save_edited_cookie(session_id, cookie_editor.toPlainText(), dialog))

        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(dialog.close)

        button_layout.addWidget(save_btn)
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

        dialog.exec()

    def save_edited_cookie(self, session_id: str, new_cookie: str, dialog):
        """Save edited cookie và reconnect"""
        if session_id not in self.sessions:
            return

        new_cookie = new_cookie.strip()
        if not new_cookie:
            QMessageBox.warning(dialog, "Error", "Cookie cannot be empty!")
            return

        # Confirm save
        reply = QMessageBox.question(dialog, "Confirm",
                                   "Save changes and reconnect session?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply != QMessageBox.Yes:
            return

        try:
            # Update cookie
            session_data = self.sessions[session_id]
            session_data.cookie = new_cookie
            session_data.is_connected = False
            session_data.status = "Disconnected"
            session_data.points = 0.0

            # Try to reconnect
            if session_data.connect():
                self.log_message(f"✅ Updated and reconnected session {session_id}")
                QMessageBox.information(dialog, "Success", "Cookie updated and reconnected successfully!")
            else:
                self.log_message(f"⚠️ Updated cookie for {session_id} but connection failed")
                QMessageBox.warning(dialog, "Warning", "Cookie updated but connection failed. Check cookie validity.")

            self.update_sessions_display()
            dialog.close()

        except Exception as e:
            self.log_message(f"❌ Failed to update cookie for {session_id}: {e}")
            QMessageBox.critical(dialog, "Error", f"Failed to update cookie: {e}")

    def reconnect_single_session(self, session_id: str):
        """Reconnect single session"""
        if session_id not in self.sessions:
            return

        session_data = self.sessions[session_id]
        self.log_message(f"🔄 Reconnecting session {session_id}...")

        if session_data.connect():
            self.log_message(f"✅ Reconnected session {session_id} - Points: {session_data.points}")
        else:
            self.log_message(f"❌ Failed to reconnect session {session_id}: {session_data.status}")

        self.update_sessions_display()

    def remove_single_cookie_from_context(self, session_id: str):
        """Remove single cookie từ context menu"""
        reply = QMessageBox.question(self, "Confirm",
                                   f"Remove cookie for session {session_id}?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            if session_id in self.sessions:
                del self.sessions[session_id]
                self.update_sessions_display()
                self.log_message(f"🗑️ Removed cookie for session {session_id}")

    # Image management methods
    def browse_single_image_file(self):
        """Browse single image file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Image File", "",
            "Image files (*.png *.jpg *.jpeg *.bmp *.gif *.webp)"
        )
        if file_path:
            self.single_file_input.setText(file_path)
            self.single_image_path = file_path
            self.update_image_preview(file_path)

    def browse_images_folder(self):
        """Browse images folder with enhanced feedback"""
        folder_path = QFileDialog.getExistingDirectory(self, "Select Images Folder")
        if folder_path:
            self.folder_input.setText(folder_path)
            self.images_folder = folder_path

            # Count images in folder
            images_list = ImageManager.get_images_from_folder(folder_path)
            image_count = len(images_list)

            if image_count > 0:
                # Show random preview
                random_image = ImageManager.get_random_image(folder_path)
                if random_image:
                    self.update_image_preview(random_image)
                    # Update preview text to show it's random
                    preview_text = f"🎲 Random Preview\n({image_count} images found)\nEach thread will get different image"
                    self.image_preview.setToolTip(preview_text)

                self.log_message(f"📁 Selected folder with {image_count} images: {os.path.basename(folder_path)}")
                self.log_message(f"🎲 Each session will randomly select from these {image_count} images")
            else:
                self.image_preview.setText("❌ No valid images found")
                self.image_preview.setToolTip("No supported image formats found in this folder")
                self.log_message(f"⚠️ No valid images found in folder: {folder_path}")

    def update_image_preview(self, image_path):
        """Cập nhật image preview"""
        try:
            pixmap = QPixmap(image_path)
            scaled_pixmap = pixmap.scaled(200, 150, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            self.image_preview.setPixmap(scaled_pixmap)
        except Exception as e:
            self.image_preview.setText(f"Preview error: {e}")

    # Prompt management methods
    def browse_prompt_file(self):
        """Browse prompt file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Prompts File", "", "Text files (*.txt)"
        )
        if file_path:
            self.prompt_file_input.setText(file_path)

    def load_prompts_from_file(self):
        """Load prompts from file with enhanced feedback"""
        file_path = self.prompt_file_input.text().strip()
        if not file_path:
            QMessageBox.warning(self, "⚠️ Warning", "Please select a prompts file first!")
            return

        try:
            # Show loading state
            self.load_prompts_btn.setText("🔄 Loading...")
            self.load_prompts_btn.setEnabled(False)
            QApplication.processEvents()

            self.prompts_list = PromptManager.load_prompts_from_file(file_path)

            # Update status with success styling
            self.prompt_status_label.setText(f"✅ Loaded {len(self.prompts_list)} prompts from file")
            self.prompt_status_label.setStyleSheet("""
                QLabel {
                    color: #4CAF50;
                    font-weight: bold;
                    padding: 8px;
                    background-color: #3a3a3a;
                    border-radius: 6px;
                    border-left: 4px solid #4CAF50;
                }
            """)

            self.log_message(f"✅ Loaded {len(self.prompts_list)} prompts from file: {os.path.basename(file_path)}")

            # Show success message with details
            QMessageBox.information(self, "🎉 Success",
                f"Successfully loaded {len(self.prompts_list)} prompts!\n\n"
                f"File: {os.path.basename(file_path)}\n"
                f"Random prompts will be used during generation.")

        except Exception as e:
            # Update status with error styling
            self.prompt_status_label.setText(f"❌ Failed to load prompts: {str(e)[:50]}...")
            self.prompt_status_label.setStyleSheet("""
                QLabel {
                    color: #f44336;
                    font-weight: bold;
                    padding: 8px;
                    background-color: #3a3a3a;
                    border-radius: 6px;
                    border-left: 4px solid #f44336;
                }
            """)

            QMessageBox.critical(self, "❌ Error", f"Failed to load prompts:\n\n{str(e)}")
            self.log_message(f"❌ Error loading prompts: {e}")

        finally:
            # Restore button state
            self.load_prompts_btn.setText("🔄 Load Prompts")
            self.load_prompts_btn.setEnabled(True)

    # Generation methods
    def execute_batch_generation(self):
        """Thực hiện batch generation với improved validation"""
        if not self.sessions:
            QMessageBox.warning(self, "Warning", "No sessions available!")
            return

        # Validate connections first
        self.log_message("🔍 Validating session connections...")
        valid_sessions = []

        for session_data in self.sessions.values():
            if session_data.is_connected:
                # Re-validate connection
                if session_data.validate_connection():
                    valid_sessions.append(session_data)
                    self.log_message(f"✅ Session {session_data.session_id} validated - Points: {session_data.points}")
                else:
                    self.log_message(f"❌ Session {session_data.session_id} connection lost")
            else:
                self.log_message(f"⚠️ Session {session_data.session_id} not connected")

        if not valid_sessions:
            QMessageBox.warning(self, "Warning",
                              "No valid connected sessions!\n\n" +
                              "Please check:\n" +
                              "• Cookie validity\n" +
                              "• Network connection\n" +
                              "• Account status")
            return

        self.log_message(f"🚀 Found {len(valid_sessions)} valid sessions")

        # Get prompt
        if self.manual_prompt_radio.isChecked():
            prompt = self.manual_prompt_input.toPlainText().strip()
            if not prompt:
                QMessageBox.warning(self, "Warning", "Please enter a prompt!")
                return
        else:
            if not self.prompts_list:
                QMessageBox.warning(self, "Warning", "Please load prompts from file!")
                return

        # Get content type
        content_type = self.content_type_combo.currentText()

        # Log image mode info
        if self.single_file_radio.isChecked():
            if self.single_image_path:
                image_info = f"using single image: {os.path.basename(self.single_image_path)}"
            else:
                image_info = "without image (text-only)"
        else:
            if self.images_folder:
                image_count = len(ImageManager.get_images_from_folder(self.images_folder))
                image_info = f"with random images from folder ({image_count} images available)"
            else:
                image_info = "without image (text-only)"

        self.log_message(f"🚀 Starting batch {content_type} generation on {len(valid_sessions)} sessions {image_info}")

        # Store batch info for sequential processing and cycles
        self.batch_content_type = content_type
        self.cycle_sessions_queue = valid_sessions.copy()  # Original sessions for cycling
        self.session_queue = valid_sessions.copy()
        self.current_session_index = 0
        self.current_cycle = 1  # Start from cycle 1

        # Log cycle information
        if self.total_cycles > 1:
            self.log_message(f"🔄 Starting generation with {self.total_cycles} cycles (Cycle {self.current_cycle}/{self.total_cycles})")

        if self.sequential_mode:
            # Sequential mode: Start timer-based sequential execution
            self.log_message(f"🔄 Sequential: Starting timer-based execution with {self.sequential_delay/1000}s delays")

            # Always initialize completion tracker for cycles (even for single cycle)
            self.cycle_completion_tracker = {}
            for session in valid_sessions:
                self.cycle_completion_tracker[session.session_id] = {
                    'generation_complete': False,
                    'download_complete': False
                }
            self.waiting_for_cycle_completion = (self.total_cycles > 1)
            self.log_message(f"🔍 Debug: Initialized tracker: {self.cycle_completion_tracker}")

            self.log_message(f"🔄 Sequential: Sending request 1/{len(valid_sessions)} (Session: {valid_sessions[0].session_id})")

            # Start first session immediately
            self.start_session_generation(valid_sessions[0], content_type)
            self.current_session_index = 1

            # Start timer for remaining sessions if any
            if len(valid_sessions) > 1:
                self.sequential_timer.start(self.sequential_delay)
        else:
            # Parallel mode: Start all API calls at once (original logic - no waiting for completion)
            self.log_message(f"⚡ Parallel: Sending all {len(valid_sessions)} requests simultaneously")

            # Execute all cycles in parallel mode (send all requests immediately)
            for cycle in range(1, self.total_cycles + 1):
                self.log_message(f"⚡ Parallel: Starting cycle {cycle}/{self.total_cycles}")

                for session_data in valid_sessions:
                    self.start_session_generation(session_data, content_type)

            if self.total_cycles > 1:
                self.log_message(f"⚡ Parallel: All {self.total_cycles} cycles started! Total requests: {self.total_cycles * len(valid_sessions)}")

            # Reset state for parallel mode (no completion tracking needed)
            self.reset_cycle_state()

        self.log_message(f"✅ Batch execution initiated successfully")

    def start_session_generation(self, session_data: SessionData, content_type: str):
        """Start generation cho một session với random image selection per thread"""
        try:
            # Get prompt for this session
            if self.manual_prompt_radio.isChecked():
                prompt = self.manual_prompt_input.toPlainText().strip()
            else:
                prompt = PromptManager.get_random_prompt(self.prompts_list)

            # Get image for this session - IMPROVED: Each thread gets random image
            image_path = None
            selected_image_name = "None"

            if self.single_file_radio.isChecked():
                # Single file mode: all sessions use same image
                image_path = self.single_image_path if self.single_image_path else None
                if image_path:
                    selected_image_name = os.path.basename(image_path)
            else:
                # Folder mode: EACH SESSION gets a DIFFERENT random image
                if self.images_folder:
                    image_path = ImageManager.get_random_image(self.images_folder)
                    if image_path:
                        selected_image_name = os.path.basename(image_path)
                        self.log_message(f"🎲 [{session_data.session_id}] Selected random image: {selected_image_name}")
                    else:
                        self.log_message(f"⚠️ [{session_data.session_id}] No valid images found in folder")

            # Prepare kwargs
            kwargs = {}
            if content_type == "video":
                kwargs['is_high_quality'] = self.high_quality_cb.isChecked()
                kwargs['auto_extend'] = self.auto_extend_cb.isChecked()
                kwargs['model_name'] = self.model_combo.currentText()

            # Create and start worker thread
            worker = UnifiedWorkerThread(session_data, content_type, prompt, image_path, **kwargs)
            worker.progress_update.connect(self.on_progress_update)
            worker.result_ready.connect(self.on_result_ready)
            worker.error_occurred.connect(self.on_error_occurred)
            worker.finished.connect(lambda: self.cleanup_finished_worker(session_data.session_id))

            self.worker_threads[session_data.session_id] = worker

            # Enhanced task description with image info
            task_desc = f"{content_type}: {prompt[:30]}..."
            if image_path:
                task_desc += f" | Img: {selected_image_name[:15]}..."

            session_data.current_task = task_desc
            session_data.progress = 0
            session_data.last_content_type = content_type
            session_data.last_model = self.model_combo.currentText() if content_type == "video" else "N/A"

            worker.start()
            self.update_sessions_display()

            # Log detailed info
            self.log_message(f"🚀 [{session_data.session_id}] Started {content_type} generation")
            self.log_message(f"📝 [{session_data.session_id}] Prompt: {prompt[:50]}...")
            if image_path:
                self.log_message(f"🖼️ [{session_data.session_id}] Using image: {selected_image_name}")

        except Exception as e:
            self.log_message(f"❌ Error starting generation for session {session_data.session_id}: {e}")

    # Signal handlers
    def on_progress_update(self, session_id: str, message: str, progress: int):
        """Xử lý progress update"""
        if session_id in self.sessions:
            self.sessions[session_id].progress = progress
            self.update_sessions_display()
            self.log_message(f"[{session_id}] {message}")

    def on_result_ready(self, session_id: str, results: List[str], content_type: str):
        """Xử lý kết quả"""
        if session_id in self.sessions:
            session_data = self.sessions[session_id]
            session_data.current_task = "Completed"
            session_data.progress = 100
            session_data.last_result = results



            # SIMPLE: Get work IDs from video generator if available
            if content_type == "video" and hasattr(session_data.video_gen, 'last_work_ids'):
                session_data.last_work_ids = session_data.video_gen.last_work_ids
                self.log_message(f"� SIMPLE: Stored work IDs: {session_data.last_work_ids}")
            else:
                self.log_message(f"❌ SIMPLE: No work IDs available")

            for i, result_url in enumerate(results):
                self.log_message(f"📋 Result {i+1}: {result_url}")

            self.update_sessions_display()
            self.add_result_to_display(session_id, results, content_type)
            self.log_message(f"✅ [{session_id}] Generated {len(results)} {content_type}(s)")

            # Enable download button
            self.download_all_btn.setEnabled(True)

            # Mark generation as complete for cycle tracking
            if session_id in self.cycle_completion_tracker:
                self.cycle_completion_tracker[session_id]['generation_complete'] = True
                self.log_message(f"✅ Generation complete for {session_id} in Cycle {self.current_cycle}")
                self.log_message(f"🔍 Debug: Tracker state = {self.cycle_completion_tracker}")

            # Trigger automatic download (independent of sequential mode)
            if self.download_settings.auto_download_enabled and self.validate_download_path():
                self.start_automatic_download(session_id, results, content_type)
            else:
                # If auto download is disabled, mark download as complete immediately
                if session_id in self.cycle_completion_tracker:
                    self.cycle_completion_tracker[session_id]['download_complete'] = True
                    self.log_message(f"✅ Download marked complete for {session_id} (auto-download disabled)")
                    self.check_cycle_completion(session_id)

    def on_error_occurred(self, session_id: str, error_message: str):
        """Handle errors with sequential mode support"""
        if session_id in self.sessions:
            session_data = self.sessions[session_id]
            session_data.current_task = f"Error: {error_message[:30]}..."
            session_data.progress = 0

            self.update_sessions_display()
            self.log_message(f"❌ [{session_id}] Error: {error_message}")

            # Mark error session as complete for cycle tracking
            if session_id in self.cycle_completion_tracker:
                self.cycle_completion_tracker[session_id]['generation_complete'] = True
                self.cycle_completion_tracker[session_id]['download_complete'] = True  # Skip download for error
                self.log_message(f"⚠️ Error session {session_id} marked as complete for cycle progression")
                self.check_cycle_completion(session_id)

    # Automatic download methods
    def start_automatic_download(self, session_id: str, results: List[str], content_type: str):
        """Bắt đầu automatic download"""
        if not results:
            return

        session_data = self.sessions.get(session_id)
        if not session_data:
            return

        model = session_data.last_model
        mode = "no watermark" if self.download_settings.no_watermark_mode else "with watermark"
        self.log_message(f"📥 Starting automatic download ({mode}) for session {session_id}")

        # If no watermark mode is enabled, try to use batch download API
        if self.download_settings.no_watermark_mode:
            for i in range(len(results)):
                self.download_without_watermark(session_id, i)
            return

        for i, url in enumerate(results):
            # Tạo download task
            download_task = DownloadTask(session_id, url, content_type, model, i + 1)
            task_id = f"{session_id}_{i + 1}"
            self.download_tasks[task_id] = download_task

            # Tạo download worker thread
            download_worker = DownloadWorkerThread(
                download_task,
                self.download_settings.download_path,
                self.download_settings.max_retries
            )

            # Connect signals
            download_worker.download_progress.connect(self.on_download_progress)
            download_worker.download_completed.connect(self.on_download_completed)
            download_worker.download_failed.connect(self.on_download_failed)
            download_worker.finished.connect(lambda: self.cleanup_finished_download(task_id))

            # Start download
            self.download_threads[task_id] = download_worker
            download_worker.start()

            self.log_message(f"📥 [{session_id}] Starting download {i + 1}/{len(results)}")

    def on_download_progress(self, task_id: str, progress: int):
        """Xử lý download progress"""
        if task_id in self.download_tasks:
            self.download_tasks[task_id].progress = progress
            self.download_tasks[task_id].status = "downloading"
            # Note: UI updates would require more complex widget tracking
            # For now, progress is logged and tracked in download_tasks

    def on_download_completed(self, task_id: str, file_path: str):
        """Handle download completed with proper cleanup"""
        if task_id in self.download_tasks:
            task = self.download_tasks[task_id]
            task.status = "completed"
            task.file_path = file_path

            filename = os.path.basename(file_path)
            self.log_message(f"✅ Download completed: {filename}")

            # Mark download as complete for cycle tracking
            session_id = task.session_id
            if session_id in self.cycle_completion_tracker:
                # Check if all downloads for this session are complete
                session_downloads_complete = True
                for tid, t in self.download_tasks.items():
                    if t.session_id == session_id and t.status != "completed":
                        session_downloads_complete = False
                        break

                if session_downloads_complete:
                    self.cycle_completion_tracker[session_id]['download_complete'] = True
                    self.log_message(f"✅ All downloads complete for {session_id} in Cycle {self.current_cycle}")
                    self.check_cycle_completion(session_id)

            # Thread cleanup will be handled by finished signal

    def on_download_failed(self, task_id: str, error_message: str):
        """Handle download failed with proper cleanup"""
        if task_id in self.download_tasks:
            task = self.download_tasks[task_id]
            task.status = "failed"
            task.error_message = error_message

            self.log_message(f"❌ Download failed: {task_id} - {error_message}")

            # Thread cleanup will be handled by finished signal

    # Display update methods
    def update_sessions_display(self):
        """Update sessions display with modern styling and header updates"""
        self.sessions_table.setRowCount(len(self.sessions))

        for row, (session_id, session_data) in enumerate(self.sessions.items()):
            # Session ID
            self.sessions_table.setItem(row, 0, QTableWidgetItem(session_id))

            # Status
            status_item = QTableWidgetItem(session_data.status)
            if session_data.is_connected:
                status_item.setBackground(QColor(76, 175, 80, 100))  # Green with transparency
            else:
                status_item.setBackground(QColor(244, 67, 54, 100))  # Red with transparency
            self.sessions_table.setItem(row, 1, status_item)

            # Points
            points_text = f"{session_data.points:.1f}" if session_data.is_connected else "N/A"
            self.sessions_table.setItem(row, 2, QTableWidgetItem(points_text))

            # Progress
            progress_text = f"{session_data.progress}%" if session_data.progress > 0 else "Idle"
            self.sessions_table.setItem(row, 3, QTableWidgetItem(progress_text))

            # Current Task
            task_text = session_data.current_task or "None"
            self.sessions_table.setItem(row, 4, QTableWidgetItem(task_text))

        # Update cookie status
        connected_count = sum(1 for s in self.sessions.values() if s.is_connected)
        total_count = len(self.sessions)
        self.cookie_status_label.setText(f"Sessions: {total_count} total, {connected_count} connected")

    def add_result_to_display(self, session_id: str, results: List[str], content_type: str):
        """Thêm kết quả vào display"""
        # Create result widget
        result_widget = QWidget()
        result_layout = QVBoxLayout(result_widget)

        # Header
        header_label = QLabel(f"Session {session_id} - {content_type.title()} Results")
        header_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        result_layout.addWidget(header_label)

        # Results
        for i, url in enumerate(results):
            url_layout = QHBoxLayout()

            # URL label (truncated)
            url_text = url if len(url) <= 60 else url[:57] + "..."
            url_label = QLabel(f"{i+1}. {url_text}")
            url_label.setWordWrap(True)

            # Download status icon
            task_id = f"{session_id}_{i + 1}"
            status_icon = QLabel("⏳")  # Default pending
            if task_id in self.download_tasks:
                task = self.download_tasks[task_id]
                if task.status == "completed":
                    status_icon.setText("✅")
                elif task.status == "failed":
                    status_icon.setText("❌")
                elif task.status == "downloading":
                    status_icon.setText("📥")
            status_icon.setMaximumWidth(20)

            # Progress bar for download
            progress_bar = QProgressBar()
            progress_bar.setMaximumWidth(100)
            progress_bar.setMaximumHeight(15)
            if task_id in self.download_tasks:
                progress_bar.setValue(self.download_tasks[task_id].progress)
            else:
                progress_bar.setValue(0)

            # Copy button
            copy_btn = QPushButton("Copy")
            copy_btn.clicked.connect(lambda checked=False, u=url: self.copy_to_clipboard(u))
            copy_btn.setMaximumWidth(50)

            # Download button - text and tooltip based on current mode
            if self.download_settings.no_watermark_mode:
                download_btn_text = "No WM"
                download_btn_tooltip = "Download without watermark (current mode)"
                download_btn_style = "QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }"
            else:
                download_btn_text = "Download"
                download_btn_tooltip = "Download with watermark (current mode)"
                download_btn_style = ""

            download_btn = QPushButton(download_btn_text)
            download_btn.clicked.connect(lambda checked=False, u=url, s=session_id, i=i, t=content_type:
                                       self.download_single_result(u, s, i, t))
            download_btn.setMaximumWidth(70)
            download_btn.setToolTip(download_btn_tooltip)
            if download_btn_style:
                download_btn.setStyleSheet(download_btn_style)

            # Download without watermark button (only for videos and when in watermark mode)
            download_no_wm_btn = None
            if content_type == "video" and not self.download_settings.no_watermark_mode:
                download_no_wm_btn = QPushButton("No WM")
                download_no_wm_btn.clicked.connect(lambda checked=False, s=session_id, i=i:
                                                 self.download_without_watermark(s, i))
                download_no_wm_btn.setMaximumWidth(60)
                download_no_wm_btn.setToolTip("Download without watermark (alternative)")
                download_no_wm_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")

            url_layout.addWidget(status_icon)
            url_layout.addWidget(url_label)
            url_layout.addWidget(progress_bar)
            url_layout.addWidget(copy_btn)
            url_layout.addWidget(download_btn)
            if download_no_wm_btn:
                url_layout.addWidget(download_no_wm_btn)

            result_layout.addLayout(url_layout)

        # Add separator
        separator = QLabel("─" * 80)
        separator.setStyleSheet("color: gray;")
        result_layout.addWidget(separator)

        # Add to results layout
        self.results_layout.addWidget(result_widget)

    def copy_to_clipboard(self, text: str):
        """Copy text to clipboard"""
        clipboard = QApplication.clipboard()
        clipboard.setText(text)
        self.log_message(f"📋 Copied to clipboard: {text[:50]}...")

    def download_single_result(self, url: str, session_id: str, index: int, content_type: str):
        """Download single result manually"""
        if not self.validate_download_path():
            QMessageBox.warning(self, "Warning", "Please set a valid download path first!")
            return

        # Check if no watermark mode is enabled and use appropriate method
        if self.download_settings.no_watermark_mode:
            self.download_without_watermark(session_id, index)
            return

        try:
            # Get session data for model info
            session_data = self.sessions.get(session_id)
            model = session_data.last_model if session_data else "unknown"

            # Create download task
            download_task = DownloadTask(session_id, url, content_type, model, index + 1)
            task_id = f"{session_id}_{index + 1}_manual"
            self.download_tasks[task_id] = download_task

            # Create download worker thread
            download_worker = DownloadWorkerThread(
                download_task,
                self.download_settings.download_path,
                self.download_settings.max_retries
            )

            # Connect signals
            download_worker.download_progress.connect(self.on_download_progress)
            download_worker.download_completed.connect(self.on_download_completed)
            download_worker.download_failed.connect(self.on_download_failed)
            download_worker.finished.connect(lambda: self.cleanup_finished_download(task_id))

            # Start download
            self.download_threads[task_id] = download_worker
            download_worker.start()

            self.log_message(f"📥 Manual download started: {session_id}_{index + 1}")

        except Exception as e:
            QMessageBox.critical(self, "Download Error", f"Failed to start download: {e}")
            self.log_message(f"❌ Manual download failed: {e}")

    def download_without_watermark(self, session_id: str, index: int):
        """Download video without watermark using batch_download_v2 API"""
        if not self.validate_download_path():
            QMessageBox.warning(self, "Warning", "Please set a valid download path first!")
            return

        if session_id not in self.sessions:
            QMessageBox.warning(self, "Warning", "Session not found!")
            return

        session_data = self.sessions[session_id]
        if not session_data.last_result or len(session_data.last_result) <= index:
            QMessageBox.warning(self, "Warning", "Result not found!")
            return

        # Get result URL and work ID
        result_url = session_data.last_result[index]

        # Get work ID from global storage
        work_id = None
        try:
            from kling.kling import LAST_WORK_IDS
            if LAST_WORK_IDS and len(LAST_WORK_IDS) > index:
                work_id = LAST_WORK_IDS[index]
            else:
                work_id = "283023275526088"  # Fallback work ID
        except Exception:
            work_id = "283023275526088"  # Fallback work ID

        if not work_id:
            QMessageBox.warning(self, "Warning", "Could not extract work ID from URL!")
            return

        try:
            # Create download task for no watermark
            download_task = DownloadTask(session_id, result_url, "video",
                                       session_data.last_model, index + 1, no_watermark=True)
            task_id = f"{session_id}_{index + 1}_no_wm"
            self.download_tasks[task_id] = download_task

            # Create no watermark download worker
            download_worker = NoWatermarkDownloadWorker(
                session_data.cookie,
                work_id,
                download_task,
                self.download_settings.download_path,
                self.download_settings.max_retries
            )

            # Connect signals
            download_worker.download_progress.connect(self.on_download_progress)
            download_worker.download_completed.connect(self.on_download_completed)
            download_worker.download_failed.connect(self.on_download_failed)
            download_worker.finished.connect(lambda: self.cleanup_finished_download(task_id))

            # Start download
            self.download_threads[task_id] = download_worker
            download_worker.start()

            self.log_message(f"📥 No watermark download started: {session_id}_{index + 1}")

        except Exception as e:
            QMessageBox.critical(self, "Download Error", f"Failed to start no watermark download: {e}")
            self.log_message(f"❌ No watermark download failed: {e}")

    def extract_work_id_from_url(self, url: str) -> str:
        """Extract work ID from result URL"""
        try:
            import re
            self.log_message(f"🔍 Extracting work ID from URL: {url}")

            # PRIORITY: Look for numeric work ID (like 283023275526088) first
            # This is what the API actually expects based on the cURL example

            # Pattern 1: Long numeric ID (12+ digits) - highest priority
            long_numeric_match = re.search(r'(\d{12,})', url)
            if long_numeric_match:
                work_id = long_numeric_match.group(1)
                self.log_message(f"✅ Extracted long numeric work ID: {work_id}")
                return work_id

            # Pattern 2: Medium numeric ID (10+ digits)
            numeric_match = re.search(r'(\d{10,})', url)
            if numeric_match:
                work_id = numeric_match.group(1)
                self.log_message(f"✅ Extracted numeric work ID: {work_id}")
                return work_id

            # Pattern 3: Traditional /works/123456/ format
            works_match = re.search(r'/works/(\d+)', url)
            if works_match:
                work_id = works_match.group(1)
                self.log_message(f"✅ Extracted works ID: {work_id}")
                return work_id

            # Pattern 4: UUID in path (fallback)
            uuid_match = re.search(r'/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})', url)
            if uuid_match:
                work_id = uuid_match.group(1)
                self.log_message(f"⚠️ Extracted UUID work ID (may not work with API): {work_id}")
                return work_id

            # Pattern 5: Base64-like ID (last resort)
            base64_matches = re.findall(r'/([A-Za-z0-9_-]{20,})', url)
            if base64_matches:
                false_positives = [
                    'ai_portal_sgp_m2v_img2video_1080p_std_v16_distill',
                    'upload-ylab-stunt-sgp',
                    'bs2',
                    'se',
                    'kling'
                ]
                for work_id in base64_matches:
                    if work_id not in false_positives and not work_id.startswith('Standard_Mode'):
                        self.log_message(f"⚠️ Extracted base64-like work ID (may not work with API): {work_id}")
                        return work_id

            self.log_message(f"❌ Could not extract work ID from URL: {url}")
            return None
        except Exception as e:
            self.log_message(f"❌ Error extracting work ID: {e}")
            return None

    def download_all_results(self):
        """Download all results using the new download system"""
        if not any(s.last_result for s in self.sessions.values()):
            QMessageBox.warning(self, "Warning", "No results to download!")
            return

        if not self.validate_download_path():
            QMessageBox.warning(self, "Warning", "Please set a valid download path first!")
            return

        try:
            download_count = 0
            mode = "no watermark" if self.download_settings.no_watermark_mode else "with watermark"
            self.log_message(f"📥 Starting batch download ({mode})")

            for session_id, session_data in self.sessions.items():
                if session_data.last_result:
                    content_type = session_data.last_content_type
                    model = session_data.last_model

                    # If no watermark mode is enabled, use batch download API
                    if self.download_settings.no_watermark_mode:
                        for i in range(len(session_data.last_result)):
                            self.download_without_watermark(session_id, i)
                            download_count += 1
                    else:
                        # Regular download with watermark
                        for i, url in enumerate(session_data.last_result):
                            # Create download task
                            download_task = DownloadTask(session_id, url, content_type, model, i + 1)
                            task_id = f"{session_id}_{i + 1}_batch"
                            self.download_tasks[task_id] = download_task

                            # Create download worker thread
                            download_worker = DownloadWorkerThread(
                                download_task,
                                self.download_settings.download_path,
                                self.download_settings.max_retries
                            )

                            # Connect signals
                            download_worker.download_progress.connect(self.on_download_progress)
                            download_worker.download_completed.connect(self.on_download_completed)
                            download_worker.download_failed.connect(self.on_download_failed)
                            download_worker.finished.connect(lambda: self.cleanup_finished_download(task_id))

                            # Start download
                            self.download_threads[task_id] = download_worker
                            download_worker.start()
                            download_count += 1

            self.log_message(f"📥 Started batch download of {download_count} files ({mode})")
            QMessageBox.information(self, "Download Started", f"Started downloading {download_count} files ({mode})!")

        except Exception as e:
            QMessageBox.critical(self, "Download Error", f"Failed to start batch download: {e}")
            self.log_message(f"❌ Batch download failed: {e}")

    def log_message(self, message: str):
        """Thêm message vào logs"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.logs_area.append(log_entry)

        # Auto scroll to bottom
        scrollbar = self.logs_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_logs(self):
        """Xóa logs"""
        self.logs_area.clear()
        self.log_message("📝 Logs cleared")


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    # Set application properties
    app.setApplicationName("KlingCreator Modern - Unified Multi-Session")
    app.setApplicationVersion("3.0")
    app.setApplicationDisplayName("KlingCreator Modern")

    # Set application icon if available
    try:
        app.setWindowIcon(QIcon("icon.png"))
    except:
        pass

    window = UnifiedMultiSessionGUI()
    window.show()

    sys.exit(app.exec())


if __name__ == '__main__':
    main()
