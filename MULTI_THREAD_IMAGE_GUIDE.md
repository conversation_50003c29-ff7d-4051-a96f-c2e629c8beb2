# 🎲 Multi-Thread Random Image Selection Guide

## 📋 Overview
KlingCreator Modern now supports **intelligent multi-threaded image selection** where each session (thread) can automatically select a different random image from a folder, instead of all sessions using the same image.

## 🔧 How It Works

### 📄 Single File Mode
- **Description**: All sessions use the same image
- **Use Case**: When you want consistent image input across all generations
- **Behavior**: One image → All threads

### 📁 Random from Folder Mode ⭐ **NEW FEATURE**
- **Description**: Each session randomly selects a different image from the folder
- **Use Case**: When you want variety and diversity in your generations
- **Behavior**: Multiple images → Each thread gets random image

## 🚀 Usage Instructions

### Step 1: Select Mode
1. In the **Image Selection** section
2. Choose **"📁 Random from Folder (Each thread gets different image)"**

### Step 2: Select Folder
1. Click **"Browse Folder"**
2. Select a folder containing your images
3. The system will show:
   - Number of valid images found
   - Random preview (changes each time)
   - Tooltip with folder info

### Step 3: Run Generation
1. Add your cookies/sessions
2. Set your prompts
3. Click **"Execute on All Sessions"**
4. Watch the logs to see which image each session selected

## 📊 What You'll See in Logs

```
🚀 Starting batch video generation on 3 sessions with random images from folder (15 images available)
🎲 [abc12345] Selected random image: sunset_mountain.jpg
🎲 [def67890] Selected random image: ocean_waves.png
🎲 [ghi13579] Selected random image: forest_path.jpg
🚀 [abc12345] Started video generation
📝 [abc12345] Prompt: A beautiful landscape...
🖼️ [abc12345] Using image: sunset_mountain.jpg
```

## 🎯 Benefits

1. **Diversity**: Each generation uses different source material
2. **Efficiency**: Automatic random selection saves time
3. **Scalability**: Works with any number of sessions
4. **Flexibility**: Easy to switch between single/multi image modes
5. **Transparency**: Clear logging shows which image each session uses

## 📁 Supported Image Formats
- `.jpg`, `.jpeg`
- `.png`
- `.bmp`
- `.gif`
- `.webp`

## 💡 Tips

1. **Organize Images**: Put similar style images in the same folder for consistent theme
2. **Image Quality**: Use high-quality images for better generation results
3. **Folder Size**: The system handles any number of images efficiently
4. **Preview**: The preview shows a random sample - actual selection happens during generation
5. **Logging**: Check the logs tab to see exactly which image each session used

## 🔍 Example Workflow

1. Create folder: `my_landscapes/`
2. Add images: `mountain1.jpg`, `ocean2.png`, `forest3.jpg`, etc.
3. Select **Random from Folder** mode
4. Browse to `my_landscapes/` folder
5. See: "🎲 Random Preview (12 images found)"
6. Run generation with 5 sessions
7. Each session automatically gets a different landscape image
8. Result: 5 unique video generations with different source images

This feature is perfect for creating diverse content batches while maintaining automation efficiency!
