# KlingCreator Modern

Modern PyQt6 interface for AI video and image generation with beautiful Material Design inspired UI.

## ✨ Features

### 🎨 Modern UI/UX
- **Material Design** inspired interface
- **Dark/Light themes** with smooth transitions
- **Card-based layout** with shadows and hover effects
- **Modern typography** with proper hierarchy
- **Responsive design** that adapts to window size
- **Smooth animations** and micro-interactions

### 🚀 Enhanced Functionality
- **Multi-session management** with visual session cards
- **Real-time status indicators** with colored status dots
- **Progress tracking** with modern progress bars
- **Drag & drop support** (planned)
- **Keyboard shortcuts** (planned)
- **Settings persistence** across sessions

### 🛠️ Technical Improvements
- **PyQt6** - Latest Qt framework
- **Modular architecture** with reusable components
- **Custom widgets** for consistent styling
- **Theme system** for easy customization
- **Error handling** with user-friendly messages

## 📦 Installation

### Requirements
- Python 3.8+
- PyQt6
- Requests
- Pillow

### Install Dependencies
```bash
pip install -r requirements_modern.txt
```

### Run Application
```bash
python run_modern.py
```

## 🎯 Usage

### 1. Launch Application
Run the launcher script:
```bash
python run_modern.py
```

### 2. Add Session
- Click "Add Session" in the left panel
- Each session represents one cookie/account
- Multiple sessions allow switching between accounts

### 3. Connect Session
- Click "Connect" on a session card
- Enter your session cookie when prompted
- Status will show "Connected" with account points

### 4. Generate Content
- Switch between Video/Image tabs
- Enter your prompt
- Configure settings (model, image input, etc.)
- Click "Generate Video" or "Generate Image"

### 5. Customize Appearance
- Use theme switcher to change between Dark/Light themes
- Settings are automatically saved

## 🏗️ Architecture

### Components
- **`kling_creator_modern.py`** - Main application
- **`modern_themes.py`** - Theme system and stylesheets
- **`modern_components.py`** - Custom UI components
- **`kling_wrapper.py`** - Safe API wrapper (reused)

### Custom Components
- **`ModernCard`** - Card container with shadow
- **`ModernButton`** - Styled buttons with hover effects
- **`StatusIndicator`** - Status dots with colors
- **`ModernProgressBar`** - Progress bar with label
- **`SessionCard`** - Complete session management card

### Theme System
- **Dark Theme** - Default dark mode with purple accents
- **Light Theme** - Clean light mode
- **Extensible** - Easy to add new themes

## 🎨 Design Principles

### Visual Hierarchy
- **Typography scale** - Title, subtitle, body, caption
- **Color system** - Primary, secondary, surface, background
- **Spacing system** - Consistent margins and padding
- **Elevation** - Cards with shadows for depth

### User Experience
- **Immediate feedback** - Status indicators and progress bars
- **Clear actions** - Prominent buttons with clear labels
- **Error prevention** - Input validation and helpful messages
- **Accessibility** - High contrast and readable fonts

### Performance
- **Lazy loading** - Components created when needed
- **Efficient updates** - Only update changed elements
- **Memory management** - Proper cleanup of resources

## 🔧 Customization

### Adding New Themes
```python
# In modern_themes.py
NEW_THEME = {
    'name': 'Custom',
    'colors': {
        'background': '#your_color',
        'primary': '#your_primary',
        # ... other colors
    }
}
```

### Creating Custom Components
```python
# Inherit from ModernCard or other base components
class CustomCard(ModernCard):
    def __init__(self):
        super().__init__("Title", "Subtitle")
        self.setup_custom_ui()
```

### Modifying Styles
```python
# Override stylesheets in components
self.setStyleSheet("""
    QWidget {
        /* Your custom styles */
    }
""")
```

## 🚧 Planned Features

### Phase 1 (Current)
- ✅ Basic UI structure
- ✅ Theme system
- ✅ Session management
- ✅ Settings persistence

### Phase 2 (Next)
- 🔄 Complete generation workflow
- 🔄 Results gallery with thumbnails
- 🔄 Download management
- 🔄 Drag & drop support

### Phase 3 (Future)
- 📋 Keyboard shortcuts
- 📋 Advanced settings panel
- 📋 Usage statistics
- 📋 Plugin system

## 🐛 Known Issues

- Generation workflow not fully implemented yet
- Results display needs enhancement
- Some animations need fine-tuning

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Follow the existing code style
4. Test thoroughly
5. Submit pull request

## 📄 License

Same as main KlingCreator project.

## 🙏 Acknowledgments

- **Material Design** - Design inspiration
- **PyQt6** - UI framework
- **Original KlingCreator** - Core functionality
